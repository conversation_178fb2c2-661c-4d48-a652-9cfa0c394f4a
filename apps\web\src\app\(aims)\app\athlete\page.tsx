"use client";

import DashboardStats from "@/components/ui/DashboardStats";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";
import { useAuth } from "@/hooks/use-auth";
import { trpc } from "@/lib/trpc/client";
import { CampaignStatus, SerializedCampaign } from "@repo/server/src/types/campaign";
import {
  BanknotesIcon,
  CurrencyDollarIcon,
  MegaphoneIcon,
} from "@heroicons/react/24/solid";
import CompleteYourProfile from "@/components/ui/CompleteYourProfile";
import CampaignCard from "@/components/ui/CampaignCard";

export default function Dashboard() {
  const { user, loading } = useAuth();
  const { data: campaigns, isLoading: campaignsLoading } =
    trpc.campaign.getCampaignsForAthlete.useQuery();
  const utils = trpc.useUtils();

  if (loading || campaignsLoading) {
    return <FullPageLoadingSpinner />;
  }

  const activeCampaigns = campaigns?.filter(
    (campaign) => campaign.status === CampaignStatus.ACTIVE,
  );
  const activeCampaignsValue = activeCampaigns?.reduce(
    (sum, campaign) => sum + (campaign.price ?? 0),
    0,
  );
  const totalEarnings = campaigns?.reduce(
    (sum, campaign) => sum + (campaign.price ?? 0),
    0,
  );
  const firstName = user?.name?.split(" ")[0];

  const stats = [
    {
      label: "Active campaigns value",
      value: `$${activeCampaignsValue?.toLocaleString() ?? 0}`,
      icon: (
        <CurrencyDollarIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
    {
      label: "Active campaigns",
      value: activeCampaigns?.length ?? 0,
      icon: (
        <MegaphoneIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
    {
      label: "Total earnings",
      value: `$${totalEarnings?.toLocaleString() ?? 0}`,
      icon: (
        <BanknotesIcon className="tw-h-6 tw-w-6 tw-text-aims-text-primary" />
      ),
    },
  ];

  const hasProfilePicture =
    !!user?.athlete?.profilePicture?.url &&
    user.athlete.profilePicture.url.startsWith("https://aims-photos");
  const hasSocialMedia =
    !!user?.athlete?.socialMedia?.instagram ||
    !!user?.athlete?.socialMedia?.twitter ||
    !!user?.athlete?.socialMedia?.tiktok;
  const hasBusinessInterests = !!user?.athlete?.businessInterests?.length;
  const hasMinPayment = !!user?.athlete?.minPayment?.shoot ||
    !!user?.athlete?.minPayment?.inPerson ||
    !!user?.athlete?.minPayment?.contentShare ||
    !!user?.athlete?.minPayment?.contentCreation ||
    !!user?.athlete?.minPayment?.giftedCollab ||
    !!user?.athlete?.minPayment?.other;
  const hasHometown = !!user?.athlete?.hometown;
  const hasPosition = !!user?.athlete?.position;
  const hasBio = !!user?.athlete?.bio;
  
  const profileCompleted =
    hasProfilePicture && hasSocialMedia && hasBusinessInterests && hasMinPayment && hasHometown && hasPosition && hasBio;

  const profileSteps = [
    {
      title: "Add a profile picture",
      description:
        "For better engagement, consider uploading a photo to personalize your profile and increase your visibility.",
      buttonText: "Add",
      completed: hasProfilePicture,
    },
    {
      title: "Link your accounts",
      description:
        "Integrate with your social media for a better experience and higher chances of being noticed.",
      buttonText: "Add",
      completed: hasSocialMedia,
    },
    {
      title: "Select interests",
      description:
        "Select your interests to receive more relevant content and recommendations.",
      buttonText: "Add",
      completed: hasBusinessInterests,
    },
    {
      title: "Set your minimum payments",
      description:
        "Set your minimum payments to receive more accurate offers.",
      buttonText: "Add",
      completed: hasMinPayment,
    },
    {
      title: "Add your hometown",
      description:
        "Add your hometown to receive more relevant offers to your area of influence.",
      buttonText: "Add",
      completed: hasHometown,
    },
    {
      title: "Add your position/event",
      description:
        "Add your position/event to receive more relevant offers.",
      buttonText: "Add",
      completed: hasPosition,
    },
    {
      title: "Add your bio",
      description:
        "Add your bio to let brands know more about you.",
      buttonText: "Add",
      completed: hasBio,
    },
  ];

  const refetchCampaigns = () => {
    utils.campaign.getCampaignsForAthlete.invalidate();
  };

  return (
    <div className="tw-p-6">
      <h2 className="tw-mb-3">Hi, {firstName}</h2>
        <DashboardStats stats={stats} />
      {!profileCompleted && <CompleteYourProfile profileSteps={profileSteps} href="/app/athlete/profile/edit" />}
      {/* Athlete's Campaigns Section */}
      <div className="tw-mb-8">
        <div className="tw-mb-4 tw-text-aims-text-primary tw-text-lg tw-font-semibold">
          Your Active Campaigns
        </div>
        {!campaigns || campaigns.length === 0 ? (
          <div className="tw-bg-aims-dark-2 tw-rounded-lg tw-p-6 tw-text-center">
            <div className="tw-text-aims-text-secondary tw-text-sm">
              You are not a part of any campaigns yet.
            </div>
          </div>
        ) : (
          <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4 sm:tw-gap-6">
            {campaigns.map((campaign: SerializedCampaign) => (
              <CampaignCard
                key={campaign.id}
                campaign={campaign}
                refetchCampaigns={refetchCampaigns}
                refetchApplications={() => {}}
                hideApplyButton
                athleteView
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
