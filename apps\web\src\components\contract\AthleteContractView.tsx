"use client";

import { useState } from "react";
import { format } from "date-fns";
import { trpc } from "@/lib/trpc/client";

import { ContractStatus, SerializedContract } from "@repo/server/src/types/contract";
import { isPdfGenerationAllowed, getPdfGenerationDisabledMessage } from "@repo/server/src/utils/contractPdf";

import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { ContractSigningModal } from "./ContractSigningModal";
import { useToast } from "../ui/toast/use-toast";
import { DeliverablesTable } from "../ui/DeliverablesTable";
import { PaymentStatusBadge, PaymentActionIndicator } from "./PaymentStatusBadge";
import { LoadingSpinner } from "../ui/LoadingSpinner";

interface AthleteContractViewProps {
  contract: SerializedContract;
  onContractSigned?: () => void;
}

export function AthleteContractView({
  contract,
  onContractSigned,
}: AthleteContractViewProps) {
  const { toast } = useToast();
  const [showSigningModal, setShowSigningModal] = useState(false);
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);

  const generatePdfMutation = trpc.contract.generatePdf.useMutation();

  // Fetch campaign deliverables to get missing fields (like daysToComplete)
  const deliverableIds = contract.terms.deliverables.map(d => d.deliverableId);
  const { data: campaignDeliverables, isLoading: deliverablesLoading } = trpc.deliverable.getByIds.useQuery(
    { deliverableIds },
    { enabled: deliverableIds.length > 0 }
  );

  // Transform contract deliverables to BaseDeliverable format for DeliverablesTable
  // This preserves the athlete-specific pricing (compensation) from the contract
  const contractDeliverablesForTable = contract.terms.deliverables.map(contractDeliverable => {
    // Find the corresponding campaign deliverable to get missing fields
    const campaignDeliverable = campaignDeliverables?.find(cd => cd.id === contractDeliverable.deliverableId);

    return {
      id: contractDeliverable.deliverableId,
      campaignId: contract.campaignId,
      name: contractDeliverable.name,
      description: contractDeliverable.description,
      daysToComplete: campaignDeliverable?.daysToComplete || 0,
      minimumPayment: contractDeliverable.compensation, // Use athlete-specific pricing from contract
      type: contractDeliverable.type as any, // Type assertion needed for compatibility
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
      // Include type-specific fields if available
      location: campaignDeliverable?.location,
      date: campaignDeliverable?.date,
      time: campaignDeliverable?.time,
      content: campaignDeliverable?.content,
      productName: campaignDeliverable?.productName,
      productPrice: campaignDeliverable?.productPrice,
    };
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case ContractStatus.PENDING_ATHLETE_SIGNATURE:
        return "tw-bg-blue-100 tw-text-blue-800";
      case ContractStatus.ATHLETE_SIGNED:
        return "tw-bg-green-100 tw-text-green-800";
      case ContractStatus.BRAND_SIGNED:
        return "tw-bg-green-100 tw-text-green-800";
      case ContractStatus.PENDING_PAYMENT:
        return "tw-bg-orange-100 tw-text-orange-800";
      case ContractStatus.PAID:
        return "tw-bg-blue-100 tw-text-blue-800";
      case ContractStatus.AWAITING_DELIVERABLES:
        return "tw-bg-purple-100 tw-text-purple-800";
      case ContractStatus.FULFILLED:
        return "tw-bg-emerald-100 tw-text-emerald-800";
      case ContractStatus.CANCELLED:
        return "tw-bg-red-100 tw-text-red-800";
      case ContractStatus.EXPIRED:
        return "tw-bg-gray-100 tw-text-gray-600";
      default:
        return "tw-bg-gray-100 tw-text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleGeneratePdf = async () => {
    setIsGeneratingPdf(true);
    try {
      const result = await generatePdfMutation.mutateAsync({
        contractId: contract.id,
        regenerate: false,
      });

      // Open PDF in new tab
      window.open(result.pdfUrl, '_blank');

      toast({
        title: "PDF Generated",
        description: "Contract PDF has been generated successfully.",
        variant: "success",
      });
    } catch (error: any) {
      console.error("Failed to generate PDF:", error);

      // Handle specific error types
      let errorMessage = "Failed to generate PDF. Please try again.";

      if (error?.message?.includes('timeout') || error?.message?.includes('Timeout')) {
        errorMessage = "PDF generation timed out. This may be due to a large contract. Please try again.";
      } else if (error?.message?.includes('Connection closed') || error?.message?.includes('Protocol error')) {
        errorMessage = "Connection was interrupted during PDF generation. Please check your internet connection and try again.";
      } else if (error?.message?.includes('Insufficient resources')) {
        errorMessage = "Server is currently busy. Please try again in a few moments.";
      } else if (error?.message?.includes('service temporarily unavailable')) {
        errorMessage = "PDF generation service is temporarily unavailable. Please try again in a moment.";
      } else if (error?.message?.includes('service is starting up')) {
        errorMessage = "PDF generation service is starting up. Please try again in a few seconds.";
      } else if (error?.message?.includes('failed after multiple attempts')) {
        errorMessage = "PDF generation failed after multiple attempts. Please contact support if this persists.";
      } else if (error?.data?.code === 'TIMEOUT') {
        errorMessage = "PDF generation took too long. Please try again.";
      } else if (error?.data?.code === 'NOT_FOUND') {
        errorMessage = "Contract data not found. Please refresh the page and try again.";
      } else if (error?.data?.code === 'FORBIDDEN') {
        errorMessage = "You don't have permission to generate this PDF.";
      }

      toast({
        title: "PDF Generation Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const handleSigningComplete = () => {
    setShowSigningModal(false);
    onContractSigned?.();
    toast({
      title: "Contract Signed",
      description: "You have successfully signed the contract! You will be notified when the brand has signed and paid to get started on the deliverables.",
      variant: "success",
    });
  };

  const isExpired = contract.expiresAt && new Date(contract.expiresAt) < new Date();
  const oneWeekFromNow = new Date();
  oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);
  const expiresWithinWeek = contract.expiresAt && new Date(contract.expiresAt) < oneWeekFromNow;
  const canSign = contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && !isExpired;

  return (
    <div className="tw-space-y-6">
      {/* Contract Header */}
      <Card>
        <CardHeader>
          <div className="tw-flex tw-justify-between tw-items-start">
            <div>
              <CardTitle className="tw-text-xl tw-font-bold">
                {contract.title}
              </CardTitle>
              <p className="tw-text-sm tw-text-aims-dark-6 tw-mt-1">
                Contract #{contract.contractNumber} • Version {contract.version}
              </p>
            </div>
            <div className="tw-flex tw-gap-2">
              <Badge className={getStatusColor(contract.status)}>
                {contract.status.replace(/_/g, ' ')}
              </Badge>
              <PaymentStatusBadge
                contractStatus={contract.status}
                paymentStatus={contract.paymentStatus}
              />
            </div>
          </div>
          
          <div className="tw-grid tw-grid-cols-1 md:tw-grid-cols-3 tw-gap-4 tw-mt-4">
            <div>
              <p className="tw-text-sm tw-font-medium tw-text-aims-dark-6">Created</p>
              <p className="tw-text-sm">{format(new Date(contract.createdAt), 'MMM dd, yyyy')}</p>
            </div>
            <div>
              <p className="tw-text-sm tw-font-medium tw-text-aims-dark-6">Campaign Period</p>
              <p className="tw-text-sm">
                {format(new Date(contract.terms.campaignDuration.startDate), 'MMM dd')} - {format(new Date(contract.terms.campaignDuration.endDate), 'MMM dd, yyyy')}
              </p>
            </div>
            <div>
              <p className="tw-text-sm tw-font-medium tw-text-aims-dark-6">Total Compensation</p>
              <p className="tw-text-lg tw-font-semibold tw-text-green-600">
                {formatCurrency(contract.terms.totalCompensation)}
              </p>
            </div>
          </div>

          {/* Expiration Warning */}
          {expiresWithinWeek && contract.status === ContractStatus.PENDING_ATHLETE_SIGNATURE && (
            <div className={`tw-mt-4 tw-p-3 tw-rounded-lg tw-border ${
              isExpired 
                ? 'tw-bg-red-50 tw-border-red-200' 
                : 'tw-bg-yellow-50 tw-border-yellow-200'
            }`}>
              <p className={`tw-text-sm ${
                isExpired ? 'tw-text-red-800' : 'tw-text-yellow-800'
              }`}>
                <span className="tw-font-medium">
                  {isExpired ? 'Expired:' : 'Expires:'}
                </span> {format(new Date(contract.expiresAt!), 'MMM dd, yyyy')}
              </p>
              {isExpired && (
                <p className="tw-text-sm tw-text-red-600 tw-mt-1">
                  This contract has expired and can no longer be signed.
                </p>
              )}
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Deliverables Section */}
      <Card>
        <CardHeader>
          <CardTitle className="tw-text-lg">Your Deliverables</CardTitle>
        </CardHeader>
        <CardContent>
          {deliverablesLoading ? (
            <div className="tw-flex tw-justify-center tw-py-8">
              <div className="tw-text-sm tw-text-gray-500">Loading deliverables...</div>
            </div>
          ) : contractDeliverablesForTable.length > 0 ? (
            <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <DeliverablesTable deliverables={contractDeliverablesForTable} />
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <div className="tw-text-sm tw-text-gray-500">No deliverables found</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className="tw-text-lg">Payment Schedule</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="tw-space-y-3">
            {contract.terms.paymentSchedule.map((payment, index) => (
              <div key={index} className="tw-flex tw-justify-between tw-items-center tw-p-3 tw-bg-green-50 tw-rounded-lg">
                <div>
                  <p className="tw-font-medium tw-text-black">{payment.description}</p>
                  {payment.milestone && (
                    <p className="tw-text-sm tw-text-aims-dark-6">Milestone: {payment.milestone}</p>
                  )}
                </div>
                <div className="tw-text-right">
                  <p className="tw-font-semibold tw-text-green-600">{formatCurrency(payment.amount)}</p>
                  <p className="tw-text-sm tw-text-aims-dark-6">
                    Due: {format(new Date(payment.dueDate), 'MMM dd, yyyy')}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Terms & Conditions */}
      {(contract.terms.additionalTerms?.length || 
        contract.terms.cancellationPolicy || 
        contract.terms.intellectualPropertyRights || 
        contract.terms.confidentialityClause) && (
        <Card>
          <CardHeader>
            <CardTitle className="tw-text-lg">Terms & Conditions</CardTitle>
          </CardHeader>
          <CardContent className="tw-space-y-4">
            {contract.terms.additionalTerms && contract.terms.additionalTerms.length > 0 && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Additional Terms</h4>
                <ul className="tw-list-disc tw-list-inside tw-space-y-1 tw-text-sm">
                  {contract.terms.additionalTerms.map((term, index) => (
                    <li key={index}>{term}</li>
                  ))}
                </ul>
              </div>
            )}

            {contract.terms.cancellationPolicy && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Cancellation Policy</h4>
                <p className="tw-text-sm">{contract.terms.cancellationPolicy}</p>
              </div>
            )}

            {contract.terms.intellectualPropertyRights && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Intellectual Property Rights</h4>
                <p className="tw-text-sm">{contract.terms.intellectualPropertyRights}</p>
              </div>
            )}

            {contract.terms.confidentialityClause && (
              <div>
                <h4 className="tw-font-medium tw-mb-2">Confidentiality</h4>
                <p className="tw-text-sm">{contract.terms.confidentialityClause}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <Card>
        <CardContent className="tw-pt-6">
          <div className="tw-flex tw-flex-wrap tw-justify-between tw-gap-3">
            <div className="tw-flex tw-flex-wrap tw-gap-3">
            <div className="tw-flex tw-flex-col tw-gap-2">
              <Button
                variant="outline"
                onClick={handleGeneratePdf}
                disabled={isGeneratingPdf || !isPdfGenerationAllowed(contract.status)}
                title={!isPdfGenerationAllowed(contract.status) ? getPdfGenerationDisabledMessage(contract.status) : undefined}
              >
                {isGeneratingPdf ?
                  <div className="tw-flex tw-items-center tw-gap-2">
                    <LoadingSpinner />
                    Generating...
                  </div>
                  : "Download PDF"}
              </Button>

              {!isPdfGenerationAllowed(contract.status) && (
                <p className="tw-text-sm tw-text-gray-500 tw-max-w-xs">
                  {getPdfGenerationDisabledMessage(contract.status)}
                </p>
              )}
            </div>

            {contract.pdfUrl && (
              <Button
                variant="outline"
                onClick={() => window.open(contract.pdfUrl, '_blank')}
              >
                View PDF
              </Button>
            )}
            </div>
            
            {canSign && (
              <Button
                onClick={() => setShowSigningModal(true)}
                className="tw-text-black"
              >
                Sign Contract
              </Button>
            )}
            
            {contract.status === ContractStatus.ATHLETE_SIGNED && (
              <div className="tw-flex tw-items-center tw-text-green-600">
                <span className="tw-text-sm tw-font-medium">
                  ✓ Signed on {contract.athleteSignedAt && format(new Date(contract.athleteSignedAt), 'MMM dd, yyyy')}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Awaiting Deliverables Status Card */}
      {contract.status === ContractStatus.AWAITING_DELIVERABLES && (
        <Card className="tw-bg-purple-50 tw-border-purple-200">
          <CardContent className="tw-py-4">
            <div className="tw-flex tw-items-center tw-gap-3">
              <div className="tw-w-8 tw-h-8 tw-bg-purple-600 tw-text-white tw-rounded-full tw-flex tw-items-center tw-justify-center tw-text-sm tw-font-medium">
                📋
              </div>
              <div>
                <h3 className="tw-font-semibold tw-text-purple-900">Ready to Start Deliverables</h3>
                <p className="tw-text-sm tw-text-purple-700">
                  Payment has been completed! You can now start working on your deliverables.
                  {contract.paymentCompletedAt && ` Payment completed on ${format(new Date(contract.paymentCompletedAt), 'MMM dd, yyyy')}.`}
                  {" "}Check the contract details below for requirements and deadlines.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Payment Action Indicator */}
      <PaymentActionIndicator
        contractStatus={contract.status}
        paymentStatus={contract.paymentStatus}
        userType="athlete"
        className="tw-mb-6"
      />

      {/* Contract Signing Modal */}
      <ContractSigningModal
        isOpen={showSigningModal}
        onClose={() => setShowSigningModal(false)}
        contract={contract}
        onSigningComplete={handleSigningComplete}
      />
    </div>
  );
}
