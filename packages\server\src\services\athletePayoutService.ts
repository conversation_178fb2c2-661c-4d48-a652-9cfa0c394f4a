import { Types } from "mongoose";
import AthleteWalletModel from "../models/athleteWallet";
import AthletePayoutRequestModel from "../models/athletePayoutRequest";
import WalletTransactionModel from "../models/walletTransaction";
import ContractModel from "../models/contract";
import AthleteModel from "../models/athlete";
import { CampaignModel } from "../models/campaign";
import { TransactionType, PayoutRequestStatus } from "../types/athlete";
import { MessageType, ChatType } from "../types/chat";
import { ExtendedTRPCError } from "../utils/trpc";
import { stripe } from "../lib/stripe";
import { createChat, sendMessage } from "../controllers/chat";
import UserModel from "../models/user";

/**
 * Sends a notification to athlete about earnings credited
 */
const sendEarningsNotification = async (
  athleteId: string,
  amount: number,
  contractTitle: string,
  contractNumber: string
) => {
  try {
    // Get athlete and user info
    const athlete = await AthleteModel.findById(athleteId);
    if (!athlete) return;

    const user = await UserModel.findById(athlete.userId);
    if (!user) return;

    // Create a self-chat for the athlete (system notification)
    const chat = await createChat(
      user._id.toString(),
      [user._id.toString()],
      ChatType.DIRECT
    );

    const message = `💰 Earnings credited! You've received $${amount.toFixed(2)} from completed contract: ${contractTitle} (${contractNumber}). The funds are now available in your wallet for withdrawal.`;

    await sendMessage(
      chat.id,
      user._id.toString(),
      message,
      MessageType.EARNINGS_CREDITED
    );

    console.log(`[Athlete Payout] Sent earnings notification to athlete ${athleteId}`);
  } catch (error) {
    console.error(`[Athlete Payout] Failed to send earnings notification:`, error);
  }
};

/**
 * Sends a notification to athlete about payout status
 */
const sendPayoutNotification = async (
  athleteId: string,
  amount: number,
  status: 'completed' | 'failed',
  errorMessage?: string
) => {
  try {
    // Get athlete and user info
    const athlete = await AthleteModel.findById(athleteId);
    if (!athlete) return;

    const user = await UserModel.findById(athlete.userId);
    if (!user) return;

    // Create a self-chat for the athlete (system notification)
    const chat = await createChat(
      user._id.toString(),
      [user._id.toString()],
      ChatType.DIRECT
    );

    let message: string;
    let messageType: MessageType;

    if (status === 'completed') {
      message = `✅ Payout completed! $${amount.toFixed(2)} has been sent to your bank account. It should appear within 1-2 business days.`;
      messageType = MessageType.PAYOUT_COMPLETED;
    } else {
      message = `❌ Payout failed: $${amount.toFixed(2)} could not be processed. ${errorMessage || 'Please try again or contact support.'}`;
      messageType = MessageType.PAYOUT_FAILED;
    }

    await sendMessage(
      chat.id,
      user._id.toString(),
      message,
      messageType
    );

    console.log(`[Athlete Payout] Sent ${status} payout notification to athlete ${athleteId}`);
  } catch (error) {
    console.error(`[Athlete Payout] Failed to send payout notification:`, error);
  }
};

/**
 * Creates or gets an athlete's wallet
 */
export const getOrCreateAthleteWallet = async (athleteId: string) => {
  let wallet = await AthleteWalletModel.findOne({ athleteId: new Types.ObjectId(athleteId) });

  if (!wallet) {
    wallet = new AthleteWalletModel({
      athleteId: new Types.ObjectId(athleteId),
      availableBalance: 0,
      pendingEarnings: 0,
      totalEarnings: 0,
    });
    await wallet.save();
  }

  return wallet;
};

/**
 * Credits athlete wallet when contract is fulfilled
 * This is called automatically when contract status changes to FULFILLED
 */
export const creditAthleteEarnings = async (contractId: string): Promise<void> => {
  console.log(`[Athlete Payout] Processing earnings for contract ${contractId}`);
  
  // Fetch the contract with populated data
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Fetch campaign for metadata
  const campaign = await CampaignModel.findById(contract.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found for contract");
  }

  // Calculate total athlete compensation from deliverables
  // This excludes AIMS fees which are already separated in the contract structure
  const totalCompensation = contract.terms.deliverables.reduce(
    (sum, deliverable) => sum + deliverable.compensation,
    0
  );

  console.log(`[Athlete Payout] Total compensation for athlete: $${totalCompensation}`);

  if (totalCompensation <= 0) {
    console.warn(`[Athlete Payout] No compensation found for contract ${contractId}`);
    return;
  }

  // Get or create athlete wallet
  const wallet = await getOrCreateAthleteWallet(contract.athleteId.toString());

  // Credit the available balance (money can be withdrawn immediately)
  wallet.availableBalance += totalCompensation;
  wallet.totalEarnings += totalCompensation;
  await wallet.save();

  // Create transaction record
  const deliverableNames = contract.terms.deliverables.map(d => d.name);
  await WalletTransactionModel.create({
    athleteId: contract.athleteId,
    type: TransactionType.EARNINGS_CREDITED,
    amount: totalCompensation,
    description: `Earnings from completed contract: ${contract.title}`,
    contractId: contract._id,
    metadata: {
      contractNumber: contract.contractNumber,
      campaignName: campaign.name,
      deliverableNames,
    },
  });

  console.log(`[Athlete Payout] Successfully credited $${totalCompensation} to athlete ${contract.athleteId}`);

  // Send notification to athlete about earnings credited
  try {
    await sendEarningsNotification(
      contract.athleteId.toString(),
      totalCompensation,
      contract.title,
      contract.contractNumber
    );
  } catch (error) {
    console.error(`[Athlete Payout] Failed to send earnings notification for contract ${contractId}:`, error);
  }
};

/**
 * Updates pending earnings when contract status changes
 * Called when contracts move to AWAITING_DELIVERABLES (add to pending)
 * Called when contracts move to FULFILLED (remove from pending, handled by creditAthleteEarnings)
 */
export const updatePendingEarnings = async (contractId: string, isAdding: boolean): Promise<void> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  const totalCompensation = contract.terms.deliverables.reduce(
    (sum, deliverable) => sum + deliverable.compensation,
    0
  );

  if (totalCompensation <= 0) return;

  const wallet = await getOrCreateAthleteWallet(contract.athleteId.toString());

  if (isAdding) {
    // Add to pending earnings when contract becomes active
    wallet.pendingEarnings += totalCompensation;
  } else {
    // Remove from pending earnings when contract is fulfilled
    wallet.pendingEarnings = Math.max(0, wallet.pendingEarnings - totalCompensation);
  }

  await wallet.save();
  console.log(`[Athlete Payout] ${isAdding ? 'Added' : 'Removed'} $${totalCompensation} ${isAdding ? 'to' : 'from'} pending earnings for athlete ${contract.athleteId}`);
};

/**
 * Gets athlete wallet with transaction history
 */
export const getAthleteWalletWithHistory = async (athleteId: string, limit: number = 50) => {
  const wallet = await getOrCreateAthleteWallet(athleteId);

  const transactions = await WalletTransactionModel
    .find({ athleteId: new Types.ObjectId(athleteId) })
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();

  return {
    wallet: wallet.toClient(),
    transactions: transactions.map(t => ({
      ...t,
      id: t._id.toString(),
      athleteId: t.athleteId.toString(),
      contractId: t.contractId?.toString(),
      payoutRequestId: t.payoutRequestId?.toString(),
    })),
  };
};

/**
 * Creates a Stripe Express account for an athlete
 */
export const createStripeConnectAccount = async (athleteId: string, athleteEmail: string) => {
  console.log(`[Stripe Connect] Creating Express account for athlete ${athleteId}`);

  

  try {
    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US',
      email: athleteEmail,
      capabilities: {
        transfers: { requested: true },
      },
      business_type: 'individual',
      business_profile: {
        url: process.env.CLIENT_URL || 'https://aimsmarketing.ai',
      },
    });

    // Update athlete wallet with Stripe account ID
    const wallet = await getOrCreateAthleteWallet(athleteId);
    wallet.stripeConnectAccountId = account.id;
    wallet.stripeAccountStatus = 'incomplete';
    await wallet.save();

    console.log(`[Stripe Connect] Created Express account ${account.id} for athlete ${athleteId}`);
    return account;
  } catch (error) {
    console.error(`[Stripe Connect] Failed to create account for athlete ${athleteId}:`, error);
    throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to create Stripe account");
  }
};

/**
 * Creates an account link for Stripe Connect onboarding
 */
export const createStripeAccountLink = async (athleteId: string, returnUrl: string, refreshUrl: string) => {
  const wallet = await AthleteWalletModel.findOne({ athleteId: new Types.ObjectId(athleteId) });

  if (!wallet?.stripeConnectAccountId) {
    throw new ExtendedTRPCError("BAD_REQUEST", "No Stripe account found for athlete");
  }

  console.log(`[Stripe Connect] Creating account link with parameters:`, {
    account: wallet.stripeConnectAccountId,
    refresh_url: refreshUrl,
    return_url: returnUrl,
    type: 'account_onboarding'
  });

  try {
    const accountLink = await stripe.accountLinks.create({
      account: wallet.stripeConnectAccountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    console.log(`[Stripe Connect] Created account link for athlete ${athleteId}:`, {
      url: accountLink.url,
      expires_at: accountLink.expires_at
    });
    return accountLink;
  } catch (error: any) {
    console.error(`[Stripe Connect] Failed to create account link for athlete ${athleteId}:`, {
      error: error.message,
      type: error.type,
      code: error.code,
      param: error.param,
      detail: error.detail,
      requestId: error.requestId,
      statusCode: error.statusCode,
      returnUrl,
      refreshUrl
    });

    // Provide more specific error messages based on Stripe error types
    let errorMessage = "Failed to create account link";
    if (error.type === 'invalid_request_error') {
      if (error.param === 'refresh_url') {
        errorMessage = `Invalid refresh URL: ${error.message}`;
      } else if (error.param === 'return_url') {
        errorMessage = `Invalid return URL: ${error.message}`;
      } else {
        errorMessage = `Invalid request: ${error.message}`;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    throw new ExtendedTRPCError("BAD_REQUEST", errorMessage);
  }
};

/**
 * Gets Stripe account status for an athlete
 */
export const getStripeAccountStatus = async (athleteId: string) => {
  const wallet = await AthleteWalletModel.findOne({ athleteId: new Types.ObjectId(athleteId) });

  if (!wallet?.stripeConnectAccountId) {
    return { hasAccount: false, status: null, accountId: null };
  }

  try {
    const account = await stripe.accounts.retrieve(wallet.stripeConnectAccountId);

    // Update wallet with current status
    const status = account.details_submitted && account.charges_enabled ? 'enabled' :
                   account.details_submitted ? 'restricted' : 'incomplete';

    wallet.stripeAccountStatus = status;
    await wallet.save();

    return {
      hasAccount: true,
      status,
      accountId: account.id,
      detailsSubmitted: account.details_submitted,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
    };
  } catch (error) {
    console.error(`[Stripe Connect] Failed to get account status for athlete ${athleteId}:`, error);
    return { hasAccount: true, status: 'error', accountId: wallet.stripeConnectAccountId };
  }
};

/**
 * Processes a payout request by creating a Stripe transfer
 */
export const processPayoutRequest = async (payoutRequestId: string): Promise<void> => {
  console.log(`[Athlete Payout] Processing payout request ${payoutRequestId}`);

  const payoutRequest = await AthletePayoutRequestModel.findById(payoutRequestId);
  if (!payoutRequest) {
    throw new ExtendedTRPCError("NOT_FOUND", "Payout request not found");
  }

  if (payoutRequest.status !== PayoutRequestStatus.PENDING) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Payout request is not in pending status");
  }

  // Get athlete wallet
  const wallet = await AthleteWalletModel.findOne({ athleteId: payoutRequest.athleteId });
  if (!wallet?.stripeConnectAccountId) {
    throw new ExtendedTRPCError("BAD_REQUEST", "No Stripe account found for athlete");
  }

  try {
    // Update status to processing
    payoutRequest.status = PayoutRequestStatus.PROCESSING;
    await payoutRequest.save();

    // Create Stripe transfer
    const amountInCents = Math.round(payoutRequest.amount * 100);
    const transfer = await stripe.transfers.create({
      amount: amountInCents,
      currency: 'usd',
      destination: wallet.stripeConnectAccountId,
      description: `Payout for athlete earnings - Request ${payoutRequestId}`,
      metadata: {
        payoutRequestId: payoutRequestId,
        athleteId: payoutRequest.athleteId.toString(),
      },
    });

    // Update payout request with Stripe transfer ID
    payoutRequest.stripePayoutId = transfer.id;
    payoutRequest.status = PayoutRequestStatus.COMPLETED;
    payoutRequest.processedAt = new Date();
    await payoutRequest.save();

    // Create transaction record
    await WalletTransactionModel.create({
      athleteId: payoutRequest.athleteId,
      type: TransactionType.PAYOUT_COMPLETED,
      amount: -payoutRequest.amount, // Negative because it's leaving the wallet
      description: `Payout completed: $${payoutRequest.amount.toFixed(2)}`,
      payoutRequestId: payoutRequest._id,
    });

    // Send success notification
    await sendPayoutNotification(
      payoutRequest.athleteId.toString(),
      payoutRequest.amount,
      'completed'
    );

    console.log(`[Athlete Payout] Successfully processed payout request ${payoutRequestId}, transfer ID: ${transfer.id}`);
  } catch (error: any) {
    console.error(`[Athlete Payout] Failed to process payout request ${payoutRequestId}:`, error);

    // Update payout request status to failed
    payoutRequest.status = PayoutRequestStatus.FAILED;
    payoutRequest.failureReason = error.message || 'Unknown error occurred';
    await payoutRequest.save();

    // Restore balance to wallet
    wallet.availableBalance += payoutRequest.amount;
    await wallet.save();

    // Create transaction record for failed payout
    await WalletTransactionModel.create({
      athleteId: payoutRequest.athleteId,
      type: TransactionType.PAYOUT_FAILED,
      amount: payoutRequest.amount, // Positive because it's being restored
      description: `Payout failed: $${payoutRequest.amount.toFixed(2)} - ${error.message || 'Unknown error'}`,
      payoutRequestId: payoutRequest._id,
    });

    // Send failure notification
    await sendPayoutNotification(
      payoutRequest.athleteId.toString(),
      payoutRequest.amount,
      'failed',
      error.message
    );

    throw error;
  }
};

/**
 * Processes all pending payout requests (to be called by a cron job or webhook)
 */
export const processPendingPayouts = async (): Promise<void> => {
  console.log(`[Athlete Payout] Processing all pending payout requests`);

  const pendingPayouts = await AthletePayoutRequestModel.find({
    status: PayoutRequestStatus.PENDING,
  }).limit(50); // Process in batches

  console.log(`[Athlete Payout] Found ${pendingPayouts.length} pending payout requests`);

  for (const payout of pendingPayouts) {
    try {
      await processPayoutRequest(payout._id.toString());
    } catch (error) {
      console.error(`[Athlete Payout] Failed to process payout ${payout._id}:`, error);
      // Continue processing other payouts even if one fails
    }
  }

  console.log(`[Athlete Payout] Finished processing pending payouts`);
};
