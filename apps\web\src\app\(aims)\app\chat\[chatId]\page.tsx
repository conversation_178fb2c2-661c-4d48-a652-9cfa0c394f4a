"use client";

import { useParams } from "next/navigation";
import { ChatRoom } from "@/components/chat/ChatRoom";
import { trpc } from "@/lib/trpc/client";
import { useAuth } from "@/hooks/use-auth";
import { FullPageLoadingSpinner } from "@/components/ui/LoadingSpinner";

export default function ChatRoomPage() {
  const params = useParams();
  const chatId = params.chatId as string;
  const { user } = useAuth();

  // Fetch chat details to get participant information
  const { data: chat, isLoading } = trpc.chat.getChatById.useQuery({
    chatId,
  });

  // Get the other participant (not the current user)
  const otherParticipant = chat?.participants.find(
    (p) => p.id !== user?.id,
  );

  if (isLoading) {
    return (
      <div className="tw-overflow-hidden tw-w-full tw-max-w-full tw-flex tw-items-center tw-justify-center tw-min-h-[400px]">
        <FullPageLoadingSpinner />
      </div>
    );
  }

  return (
    <div className="tw-overflow-hidden tw-w-full tw-max-w-full">
      <ChatRoom chatId={chatId} otherParticipant={otherParticipant} />
    </div>
  );
}
