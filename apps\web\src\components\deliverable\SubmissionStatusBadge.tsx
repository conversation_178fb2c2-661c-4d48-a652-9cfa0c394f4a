"use client";

import { cn } from "@/lib/utils";
import { 
  DeliverableSubmissionStatus, 
  SubmissionStatusBadgeProps 
} from "@/types/deliverableSubmission";

export function SubmissionStatusBadge({ status, className }: SubmissionStatusBadgeProps) {
  const getStatusConfig = (status: DeliverableSubmissionStatus) => {
    switch (status) {
      case DeliverableSubmissionStatus.PENDING:
        return {
          label: "Pending Review",
          className: "tw-bg-yellow-100 tw-text-yellow-800 tw-border-yellow-200",
        };
      case DeliverableSubmissionStatus.APPROVED:
        return {
          label: "Approved",
          className: "tw-bg-green-100 tw-text-green-800 tw-border-green-200",
        };
      case DeliverableSubmissionStatus.REJECTED:
        return {
          label: "Rejected",
          className: "tw-bg-red-100 tw-text-red-800 tw-border-red-200",
        };
      case DeliverableSubmissionStatus.NEEDS_REVISION:
        return {
          label: "Needs Revision",
          className: "tw-bg-orange-100 tw-text-orange-800 tw-border-orange-200",
        };
      default:
        return {
          label: "Unknown",
          className: "tw-bg-gray-100 tw-text-gray-800 tw-border-gray-200",
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span
      className={cn(
        "tw-inline-flex tw-items-center tw-px-2 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium tw-border",
        config.className,
        className
      )}
    >
      {config.label}
    </span>
  );
}
