import * as yup from "yup";

import {
  submitDeliverable,
  getAthleteSubmissions,
  getCampaignSubmissions,
  hasActiveContract,
  reviewSubmission,
  getSubmissionForResubmission,
} from "../controllers/deliverableSubmission";
import { privateProcedure, trpc } from "../lib/trpc";

// Validation schemas
const deliverableSubmissionFileSchema = yup.object({
  url: yup.string().required("File URL is required"),
  originalName: yup.string().required("Original file name is required"),
  fileType: yup.string().required("File type is required"),
  fileSize: yup.number().required("File size is required").min(1, "File size must be greater than 0"),
});

const submitDeliverableSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
  deliverableId: yup.string().required("Deliverable ID is required"),
  description: yup.string().required("Description is required").max(2000, "Description too long"),
  files: yup.array().of(deliverableSubmissionFileSchema).default([]),
});

const getAthleteSubmissionsSchema = yup.object({
  campaignId: yup.string().optional(),
});

const getCampaignSubmissionsSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
});

const hasActiveContractSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
});

const getSubmissionSchema = yup.object({
  campaignId: yup.string().required("Campaign ID is required"),
  deliverableId: yup.string().required("Deliverable ID is required"),
});

const reviewSubmissionSchema = yup.object({
  submissionId: yup.string().required("Submission ID is required"),
  status: yup.string().oneOf(["PENDING", "APPROVED", "REJECTED", "NEEDS_REVISION"]).required("Status is required"),
  feedback: yup.string().max(2000, "Feedback too long").optional(),
});

export const deliverableSubmissionRouter = trpc.router({
  // Submit a deliverable
  submit: privateProcedure
    .input(submitDeliverableSchema)
    .mutation(async ({ ctx, input }) => {
      return submitDeliverable(
        ctx.req.user.id,
        ctx.req.user.userType,
        input
      );
    }),

  // Get submissions for an athlete
  getAthleteSubmissions: privateProcedure
    .input(getAthleteSubmissionsSchema)
    .query(async ({ ctx, input }) => {
      return getAthleteSubmissions(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.campaignId
      );
    }),

  // Get submissions for a campaign (brand view)
  getCampaignSubmissions: privateProcedure
    .input(getCampaignSubmissionsSchema)
    .query(async ({ ctx, input }) => {
      return getCampaignSubmissions(
        ctx.req.user.id,
        ctx.req.user.userType,
        input.campaignId
      );
    }),

  // Check if athlete has active contract
  hasActiveContract: privateProcedure
    .input(hasActiveContractSchema)
    .query(async ({ ctx, input }) => {
      return hasActiveContract(
        ctx.req.user.id,
        input.campaignId
      );
    }),

  // Review a submission (brand only)
  reviewSubmission: privateProcedure
    .input(reviewSubmissionSchema)
    .mutation(async ({ ctx, input }) => {
      return reviewSubmission(
        ctx.req.user.id,
        ctx.req.user.userType,
        input as any
      );
    }),

  // Get submission for resubmission (athlete only)
  getSubmissionForResubmission: privateProcedure
    .input(getSubmissionSchema)
    .query(async ({ ctx, input }) => {
      return getSubmissionForResubmission(
        ctx.req.user.id,
        ctx.req.user.userType,
        input
      );
    }),
});
