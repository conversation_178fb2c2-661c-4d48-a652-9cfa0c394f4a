"use client";

import { useState, useEffect, useRef } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { trpc } from "@/lib/trpc/client";

import { SerializedContract } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Checkbox } from "../ui/auth/Checkbox";
import { useToast } from "../ui/toast/use-toast";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { StepIndicator } from "../ui/StepIndicator";

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface BrandContractSigningModalProps {
  isOpen: boolean;
  onClose: () => void;
  contract: SerializedContract;
  onSigningComplete?: () => void;
}

// Step 1: Contract Review & Agreement
interface ContractReviewStepProps {
  contract: SerializedContract;
  onNext: () => void;
  onClose: () => void;
}

// Step 2: Payment Processing
interface PaymentStepProps {
  contract: SerializedContract;
  onComplete: () => void;
  onClose: () => void;
}

export function BrandContractSigningModal({
  isOpen,
  onClose,
  contract,
  onSigningComplete,
}: BrandContractSigningModalProps) {
  const [currentStep, setCurrentStep] = useState(1);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      // If contract is already signed (PENDING_PAYMENT status), skip to payment step
      if (contract.status === "PENDING_PAYMENT" || contract.status === "BRAND_SIGNED") {
        setCurrentStep(2);
      } else {
        setCurrentStep(1);
      }
    }
  }, [isOpen, contract.status]);

  const handleContractSigned = () => {
    setCurrentStep(2);
  };

  const handlePaymentComplete = () => {
    onSigningComplete?.();
    onClose();
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="tw-max-w-[calc(100vw-2rem)] sm:tw-max-w-[calc(100vw-3rem)] lg:tw-max-w-5xl">
        <DialogHeader>
          <DialogTitle>
            {currentStep === 1 ? "Sign Contract" : "Complete Payment"}
          </DialogTitle>
          <DialogDescription>
            {currentStep === 1
              ? "Please review and confirm your agreement to the contract terms before signing as the brand representative."
              : "Complete your payment to activate the contract and notify the athlete."
            }
          </DialogDescription>
        </DialogHeader>

        {/* Step Indicator */}
        <StepIndicator
          currentStep={currentStep}
          totalSteps={2}
          className="tw-border-b tw-border-gray-200 tw-pb-4"
        />

        {/* Step Content */}
        <div className="tw-flex-1 tw-overflow-y-auto">
          {currentStep === 1 ? (
            <ContractReviewStep
              contract={contract}
              onNext={handleContractSigned}
              onClose={handleClose}
            />
          ) : (
            <PaymentStep
              contract={contract}
              onComplete={handlePaymentComplete}
              onClose={handleClose}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Step 1: Contract Review & Agreement Component
function ContractReviewStep({ contract, onNext, onClose }: ContractReviewStepProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreements, setAgreements] = useState({
    termsAccepted: false,
    paymentCommitment: false,
    deliverableApproval: false,
    legalBinding: false,
  });

  const signContractMutation = trpc.contract.sign.useMutation();

  const handleAgreementChange = (field: keyof typeof agreements, checked: boolean) => {
    setAgreements(prev => ({
      ...prev,
      [field]: checked,
    }));
  };

  const allAgreementsChecked = Object.values(agreements).every(Boolean);

  const handleSign = async () => {
    if (!allAgreementsChecked) {
      toast({
        title: "Agreement Required",
        description: "Please check all agreement boxes before signing.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Get user's IP address and user agent for signature data
      const signatureData = {
        ipAddress: await getUserIpAddress(),
        userAgent: navigator.userAgent,
      };

      await signContractMutation.mutateAsync({
        contractId: contract.id,
        signatureData,
      });

      toast({
        title: "Contract Signed",
        description: "Contract signed successfully. Please complete payment to activate the contract.",
        variant: "default",
      });

      onNext();
    } catch (error) {
      console.error("Failed to sign contract:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to sign contract. Please try again.";
      toast({
        title: "Signing Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="tw-flex tw-flex-col tw-h-full">
      <div className="tw-flex-1 tw-space-y-6 tw-py-4">
        {/* Contract Summary */}
        <div className="tw-bg-gray-50 tw-p-4 tw-rounded-lg">
          <h4 className="tw-font-medium tw-mb-2 tw-text-black">Contract Summary</h4>
          <div className="tw-grid tw-grid-cols-2 tw-gap-4 tw-text-sm">
            <div>
              <span className="tw-text-gray-600">Contract:</span>
              <p className="tw-font-medium tw-text-black">{contract.contractNumber}</p>
            </div>
            <div>
              <span className="tw-text-gray-600">Total Compensation:</span>
              <p className="tw-font-medium tw-text-green-600">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD',
                }).format(contract.terms.totalCompensation)}
              </p>
            </div>
            <div>
              <span className="tw-text-gray-600">Deliverables:</span>
              <p className="tw-font-medium tw-text-black">{contract.terms.deliverables.length} items</p>
            </div>
            <div>
              <span className="tw-text-gray-600">Campaign Duration:</span>
              <p className="tw-font-medium tw-text-black">
                {new Date(contract.terms.campaignDuration.startDate).toLocaleDateString()} - {new Date(contract.terms.campaignDuration.endDate).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>

        {/* Agreement Checkboxes */}
        <div className="tw-space-y-4">
          <h4 className="tw-font-medium">Please confirm your agreement as the brand representative:</h4>

          <div className="tw-space-y-3">
            <Checkbox
              id="termsAccepted"
              label="I have read and agree to all terms and conditions outlined in this contract on behalf of my organization."
              checked={agreements.termsAccepted}
              onChange={(checked) => handleAgreementChange('termsAccepted', checked)}
            />
            <Checkbox
              id="paymentCommitment"
              label="I commit to making all payments according to the payment schedule and terms specified in this contract."
              checked={agreements.paymentCommitment}
              onChange={(checked) => handleAgreementChange('paymentCommitment', checked)}
            />
            <Checkbox
              id="deliverableApproval"
              label="I understand the deliverable requirements and commit to providing timely feedback and approval as outlined in this contract."
              checked={agreements.deliverableApproval}
              onChange={(checked) => handleAgreementChange('deliverableApproval', checked)}
            />
            <Checkbox
              id="legalBinding"
              label="I understand that this is a legally binding agreement and that my electronic signature has the same legal effect as a handwritten signature."
              checked={agreements.legalBinding}
              onChange={(checked) => handleAgreementChange('legalBinding', checked)}
            />
          </div>
        </div>

        {/* Digital Signature Notice */}
        <div className="tw-bg-blue-50 tw-p-4 tw-rounded-lg tw-border tw-border-blue-200">
          <h4 className="tw-font-medium tw-text-blue-900 tw-mb-2">Digital Signature</h4>
          <p className="tw-text-sm tw-text-blue-800">
            By clicking &quot;Sign Contract&quot; below, you are providing your electronic signature to this contract as an authorized representative of your organization.
            Your signature will be recorded along with your IP address and timestamp for legal verification.
          </p>
        </div>
      </div>

      <DialogFooter className="tw-flex tw-justify-between tw-mt-2 tw-mb-4">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isSubmitting}
          className="tw-text-aims-text-primary"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSign}
          disabled={!allAgreementsChecked || isSubmitting}
        >
          {isSubmitting ? "Signing..." : "Sign Contract"}
        </Button>
      </DialogFooter>
    </div>
  );
}

// Step 2: Payment Processing Component
function PaymentStep({ contract, onComplete, onClose }: PaymentStepProps) {
  const { toast } = useToast();
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentAmount, setPaymentAmount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const initializingRef = useRef(false);

  const createPaymentIntentMutation = trpc.contract.createPaymentIntent.useMutation();

  // Initialize payment when component mounts
  useEffect(() => {
    const initializePayment = async () => {
      if (initializingRef.current) return;
      initializingRef.current = true;
      setIsLoading(true);

      try {
        const result = await createPaymentIntentMutation.mutateAsync({
          contractId: contract.id,
        });

        setClientSecret(result.clientSecret);
        // Convert from cents to dollars
        setPaymentAmount(result.amount / 100);
        console.log("Payment amount:", result.amount / 100);
      } catch (error) {
        console.error("Failed to initialize payment:", error);
        toast({
          title: "Payment Setup Failed",
          description: "Failed to initialize payment. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializePayment();
  }, [contract.id, createPaymentIntentMutation, toast]);

  if (isLoading) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-py-8">
        <LoadingSpinner />
        <span className="tw-ml-2">Setting up payment...</span>
      </div>
    );
  }

  if (!clientSecret) {
    return (
      <div className="tw-text-center tw-py-8">
        <p className="tw-text-red-600">Failed to initialize payment</p>
        <Button
          onClick={() => {
            setClientSecret(null);
            initializingRef.current = false;
          }}
          className="tw-mt-4"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        clientSecret,
        appearance: {
          theme: 'stripe',
          variables: {
            colorPrimary: '#0570de',
            colorBackground: '#ffffff',
            colorText: '#30313d',
            colorDanger: '#df1b41',
            fontFamily: 'system-ui, sans-serif',
            spacingUnit: '4px',
            borderRadius: '6px',
            fontSizeBase: '16px',
          },
          rules: {
            '.Input': {
              border: '1px solid #e2e8f0',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            },
            '.Input:focus': {
              border: '1px solid #0570de',
              boxShadow: '0 0 0 2px rgba(5, 112, 222, 0.1)',
            },
            '.Input--invalid': {
              border: '1px solid #df1b41',
              boxShadow: '0 0 0 2px rgba(223, 27, 65, 0.1)',
            },
          },
        },
      }}
    >
      <PaymentForm
        contract={contract}
        paymentAmount={paymentAmount}
        onPaymentComplete={onComplete}
        onClose={onClose}
      />
    </Elements>
  );
}

// Payment Form Component (used within Stripe Elements)
function PaymentForm({
  contract,
  paymentAmount,
  onPaymentComplete,
  onClose
}: {
  contract: SerializedContract;
  paymentAmount: number;
  onPaymentComplete: () => void;
  onClose: () => void;
}) {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/app/brand/contracts`,
        },
        redirect: "if_required",
      });

      if (error) {
        console.error("Payment failed:", error);
        toast({
          title: "Payment Failed",
          description: error.message || "An error occurred while processing your payment.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Payment Successful",
          description: "Your payment has been processed successfully. The contract is now active and the athlete has been notified.",
          variant: "default",
        });
        onPaymentComplete();
      }
    } catch (error) {
      console.error("Payment error:", error);
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred. Please try again.";
      toast({
        title: "Payment Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="tw-flex tw-flex-col tw-h-full">
      <div className="tw-flex-1 tw-space-y-6 tw-py-4">
        {/* Contract Summary */}
        <div className="tw-bg-gray-50 tw-p-4 tw-rounded-lg">
          <h3 className="tw-font-semibold tw-text-lg tw-mb-2 tw-text-black">Payment Summary</h3>
          <div className="tw-space-y-2">
            <div className="tw-flex tw-justify-between">
              <span>Contract:</span>
              <span className="tw-font-medium">{contract.title}</span>
            </div>
            <div className="tw-flex tw-justify-between">
              <span>Contract Number:</span>
              <span className="tw-font-mono tw-text-sm">{contract.contractNumber}</span>
            </div>
            <div className="tw-flex tw-justify-between tw-text-lg tw-font-semibold tw-border-t tw-pt-2">
              <span>Total Amount:</span>
              <span>
                {paymentAmount.toLocaleString("en-US", {
                  style: "currency",
                  currency: "USD",
                })}
              </span>
            </div>
          </div>
        </div>

        {/* Payment Method */}
        <div>
          <label className="tw-block tw-text-sm tw-font-medium tw-mb-2 tw-text-aims-text-primary">
            Payment Method
          </label>
          <PaymentElement />
        </div>
      </div>

      {/* Payment Button */}
      <DialogFooter className="tw-flex tw-justify-between tw-mt-2 tw-mb-4">
        <Button
          type="button"
          variant="outline"
          onClick={onClose}
          disabled={isProcessing}
          className="tw-text-aims-text-primary"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!stripe || isProcessing}
          className="tw-min-w-[120px]"
        >
          {isProcessing ? (
            <>
              <LoadingSpinner />
              Processing...
            </>
          ) : (
            `Pay ${paymentAmount.toLocaleString("en-US", {
              style: "currency",
              currency: "USD",
            })}`
          )}
        </Button>
      </DialogFooter>
    </form>
  );
}

// Helper function to get user's IP address
async function getUserIpAddress(): Promise<string> {
  try {
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip || 'Unknown';
  } catch (error) {
    console.warn('Failed to get IP address:', error);
    return 'Unknown';
  }
}
