import Image from "next/image";
import Link from "next/link";
import {
  AcademicCapIcon,
  BoltIcon,
  HomeIcon,
  UserIcon,
} from "@heroicons/react/24/outline";

import { SerializedAthleteProfile } from "@repo/server/src/models/user";

import { But<PERSON> } from "./button";
import SendMessageButton from "./SendMessageButton";

type AthleteWithUser = Omit<SerializedAthleteProfile, "userId"> & {
  userId: {
    _id: string;
    name: string;
  };
  _id: string;
};

interface AthleteCardProps {
  athlete: AthleteWithUser;
  isSelected?: boolean;
  onSelect?: (athleteId: string) => void;
}

export default function AthleteCard({
  athlete,
  isSelected = false,
  onSelect,
}: AthleteCardProps) {
  const hasSocialMedia =
    !!athlete.socialMedia?.instagram ||
    !!athlete.socialMedia?.twitter ||
    !!athlete.socialMedia?.tiktok;

  return (
    <div
      className={`tw-bg-aims-dark-2 tw-rounded-lg tw-overflow-hidden tw-relative tw-flex tw-flex-col tw-h-full tw-transition-all tw-duration-200 ${isSelected ? "tw-ring-2 tw-ring-aims-primary" : ""}`}
    >
      {/* Selection Checkbox */}
      <div className="tw-absolute tw-top-2 tw-left-2 tw-z-10">
        <label className="tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-cursor-pointer">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect?.(athlete._id)}
            className="tw-w-5 tw-h-5 sm:tw-w-6 sm:tw-h-6 tw-rounded tw-border-2 tw-border-aims-text-secondary tw-bg-aims-primary tw-cursor-pointer hover:tw-border-aims-accent focus:tw-ring-aims-primary tw-transition-all"
          />
        </label>
      </div>

      {/* Full width image at top */}
      <div
        className="tw-relative tw-w-full tw-h-40 sm:tw-h-48 tw-cursor-pointer"
        onClick={() => onSelect?.(athlete._id)}
      >
        <Image
          src={athlete.profilePicture?.url || "/no-profile-pic.jpg"}
          alt={athlete.userId.name}
          fill
          className="tw-object-cover tw-shadow-lg"
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
        />
      </div>

      {/* Content below image */}
      <div className="tw-p-4 sm:tw-p-6 tw-flex tw-flex-col tw-gap-3 sm:tw-gap-4 tw-flex-1">
        <div className="tw-flex tw-flex-col tw-gap-2">
          <div className="tw-flex tw-flex-col sm:tw-flex-row tw-justify-between tw-items-start sm:tw-items-center tw-gap-2 sm:tw-gap-0">
            <span className="tw-text-base sm:tw-text-lg tw-font-semibold tw-text-aims-text-primary tw-leading-tight">
              {athlete.name || athlete.userId.name}
            </span>
            <Link
              className="tw-text-aims-primary tw-text-sm sm:tw-text-base tw-inline-flex tw-items-center tw-min-h-[44px] tw-self-start sm:tw-self-auto"
              href={`/app/athlete/${athlete._id}`}
            >
              View Profile
            </Link>
          </div>
          <div className="tw-text-aims-text-secondary tw-gap-2 tw-flex tw-flex-col tw-text-xs sm:tw-text-sm">
            <div className="tw-flex tw-items-center tw-gap-2">
              <AcademicCapIcon className="tw-w-4 tw-h-4 tw-flex-shrink-0" />
              <span className="tw-truncate">{athlete.university} ({athlete.yearInSchool})</span>
            </div>
            <div className="tw-flex tw-items-center tw-gap-2">
              <BoltIcon className="tw-w-4 tw-h-4 tw-flex-shrink-0" />
              <span className="tw-truncate">{athlete.sport} {athlete.position && `- ${athlete.position}`}</span>
            </div>
            {athlete.hometown && (
              <div className="tw-flex tw-items-center tw-gap-2">
                <HomeIcon className="tw-w-4 tw-h-4 tw-flex-shrink-0" />
                <span className="tw-truncate">{athlete.hometown}</span>
              </div>
            )}
          </div>
        </div>

        {/* Bio */}
        {athlete.bio && (
          <div className="tw-text-aims-text-secondary tw-text-xs sm:tw-text-sm tw-line-clamp-2 tw-leading-relaxed">
            {athlete.bio}
          </div>
        )}

        {/* Social Media */}
        {hasSocialMedia && (
          <div className="tw-flex tw-gap-2 sm:tw-gap-3">
            {athlete.socialMedia?.instagram && (
              <a
                href={athlete.socialMedia.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="tw-text-aims-text-secondary hover:tw-text-aims-text-primary tw-flex tw-items-center tw-justify-center tw-min-w-[44px] tw-min-h-[44px] tw-p-2"
                aria-label="View Instagram profile"
              >
                <Image
                  src="/icons/instagram.svg"
                  alt="Instagram"
                  width={32}
                  height={32}
                  className="sm:tw-w-10 sm:tw-h-10"
                />
              </a>
            )}
            {athlete.socialMedia?.tiktok && (
              <a
                href={athlete.socialMedia.tiktok}
                target="_blank"
                rel="noopener noreferrer"
                className="tw-text-aims-text-secondary hover:tw-text-aims-text-primary tw-flex tw-items-center tw-justify-center tw-min-w-[44px] tw-min-h-[44px] tw-p-2"
                aria-label="View TikTok profile"
              >
                <Image
                  src="/icons/tiktok.svg"
                  alt="TikTok"
                  width={32}
                  height={32}
                  className="sm:tw-w-10 sm:tw-h-10"
                />
              </a>
            )}
            {athlete.socialMedia?.twitter && (
              <a
                href={athlete.socialMedia.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="tw-text-aims-text-secondary hover:tw-text-aims-text-primary tw-flex tw-items-center tw-justify-center tw-min-w-[44px] tw-min-h-[44px] tw-p-2"
                aria-label="View X (Twitter) profile"
              >
                <Image
                  src="/icons/x.svg"
                  alt="X"
                  width={32}
                  height={32}
                  className="sm:tw-w-10 sm:tw-h-10"
                />
              </a>
            )}
          </div>
        )}

        {/* Business Interests */}
        {athlete.businessInterests?.length > 0 && (
          <div className="tw-flex tw-flex-wrap tw-gap-2 tw-overflow-hidden">
            {athlete.businessInterests.slice(0, 3).map((interest) => (
              <span
                key={interest}
                className="tw-bg-aims-dark-4 tw-text-xs tw-rounded-full tw-px-3 tw-py-1 tw-text-aims-text-primary tw-font-medium"
              >
                {interest}
              </span>
            ))}
            {athlete.businessInterests.length > 3 && (
              <Link
                href={`/app/athlete/${athlete._id}`}
                className="tw-text-aims-text-secondary tw-text-xs tw-inline-flex tw-items-center tw-min-h-[44px] tw-px-2"
              >
                +{athlete.businessInterests.length - 3} more
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Buttons always at the bottom */}
      <div className="tw-px-4 sm:tw-px-6 tw-pb-4 sm:tw-pb-6 tw-mt-auto tw-flex tw-flex-col tw-gap-2 sm:tw-gap-3">
        <Button
          variant="outline"
          className="tw-w-full tw-text-aims-text-primary tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base"
          asChild
        >
          <Link href={`/app/athlete/${athlete._id}`} className="tw-w-full tw-flex tw-items-center tw-justify-center tw-min-h-[44px]">
            <div className="tw-flex tw-flex-row tw-items-center tw-gap-2">
              <UserIcon className="tw-w-4 tw-h-4" />
              <span>View Profile</span>
            </div>
          </Link>
        </Button>
        <SendMessageButton
          targetUserId={athlete.userId._id}
          targetName={athlete.name || athlete.userId.name}
          variant="default"
          className="tw-w-full tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base"
        />
      </div>
    </div>
  );
}
