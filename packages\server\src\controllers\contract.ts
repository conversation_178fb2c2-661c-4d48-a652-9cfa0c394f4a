import { Types } from "mongoose";

import AthleteModel from "../models/athlete";
import BrandModel from "../models/brand";
import { CampaignApplicationModel, CampaignModel, serializeCampaign } from "../models/campaign";
import ContractModel from "../models/contract";
import DeliverableModel from "../models/deliverable";
import { UserType } from "../models/user";
import { pdfMakeGenerator } from "../services/pdfMakeGenerator";
import { ApplicationStatus } from "../types/campaign";
import {
  ContractGenerationInput,
  ContractUpdateInput,
  ContractStatus,
  ContractType,
  SerializedContract,
} from "../types/contract";
import { deleteFile, uploadPdfDirectly } from "../utils/s3";
import { ExtendedTRPCError } from "../utils/trpc";
import { isPdfGenerationAllowed, isContractEditingAllowed } from "../utils/contractPdf";
import { MessageType, ChatType } from "../types/chat";
import { createChat, sendMessage } from "./chat";
import {
  createContractPaymentIntent,
  getContractPaymentStatus,
  markContractFulfilled,
  retryContractPayment,
  syncPaymentStatus
} from "../services/paymentService";
import { updatePendingEarnings } from "../services/athletePayoutService";

// Helper function to send contract notifications
const sendContractNotification = async (
  contract: any,
  messageType: MessageType,
  content: string,
  senderUserId: string,
) => {
  try {
    console.log(`Starting contract notification for contract ${contract._id}, messageType: ${messageType}, senderUserId: ${senderUserId}`);

    // Get brand and athlete documents
    const brand = await BrandModel.findById(contract.brandId);
    const athlete = await AthleteModel.findById(contract.athleteId);

    if (!brand || !athlete) {
      console.error("Failed to find brand or athlete for contract notification", {
        brandId: contract.brandId,
        athleteId: contract.athleteId,
        foundBrand: !!brand,
        foundAthlete: !!athlete
      });
      return;
    }

    // Get the brand and athlete user IDs
    const brandUserId = brand.userId.toString();
    const athleteUserId = athlete.userId.toString();

    console.log(`Brand userId: ${brandUserId}, Athlete userId: ${athleteUserId}`);

    // Determine recipient based on sender
    const recipientUserId = senderUserId === brandUserId ? athleteUserId : brandUserId;

    console.log(`Sending contract notification from ${senderUserId} to ${recipientUserId}`);

    // Create or get the chat between brand and athlete
    const chat = await createChat(
      senderUserId,
      [recipientUserId],
      ChatType.DIRECT,
      contract.campaignId?.toString()
    );

    console.log(`Created/found chat: ${chat.id}`);

    // Send the notification message
    await sendMessage(
      chat.id,
      senderUserId,
      content,
      messageType,
      contract.campaignId?.toString(),
      contract._id.toString()
    );

    console.log(`Contract notification sent successfully - messageType: ${messageType}, content: ${content}`);
  } catch (error) {
    // Log the error but don't fail the contract operation
    console.error("Failed to send contract notification:", error);
  }
};

export const generateContract = async (
  userId: string,
  userType: UserType,
  input: ContractGenerationInput,
): Promise<SerializedContract> => {
  // Only brands can generate contracts
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can generate contracts",
    );
  }

  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  // Verify campaign belongs to this brand
  const campaign = await CampaignModel.findById(input.campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  if (campaign.brandId.toString() !== brand._id.toString()) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to create contracts for this campaign",
    );
  }

  // Verify application exists and is accepted
  const application = await CampaignApplicationModel.findById(
    input.applicationId,
  );
  if (!application) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign application not found");
  }

  if (application.status !== ApplicationStatus.ACCEPTED) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Can only generate contracts for accepted applications",
    );
  }

  if (application.campaignId.toString() !== campaign._id.toString()) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Application does not belong to this campaign",
    );
  }

  // Check if contract already exists
  const existingContract = await ContractModel.findOne({
    campaignId: campaign._id,
    applicationId: application._id,
  });

  if (existingContract) {
    throw new ExtendedTRPCError(
      "CONFLICT",
      "Contract already exists for this application",
    );
  }

  // Get athlete information
  const athlete = await AthleteModel.findById(application.athleteId);
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete not found");
  }

  // Get deliverables for the campaign
  const deliverables = await DeliverableModel.find({
    campaignId: campaign._id,
  });

  console.log("application", application);

  // Log application deliverables for debugging
  if (application.deliverables && application.deliverables.length > 0) {
    console.log(`Found ${application.deliverables.length} athlete-specific deliverables in application`);
  } else {
    console.log("No athlete-specific deliverables found in application, using campaign defaults");
  }

  // Calculate total compensation from application deliverables or use campaign defaults
  const contractDeliverables = deliverables.map((deliverable) => {
    const appDeliverable = application.deliverables?.find(
      (d: any) => d.name === deliverable.name,
    );

    // Prioritize application-specific pricing (from invitations) over campaign baseline
    const compensation = appDeliverable?.minimumPayment || deliverable.minimumPayment;

    // Log pricing source for debugging
    if (appDeliverable?.minimumPayment) {
      console.log(`Using athlete-specific pricing for ${deliverable.name}: $${appDeliverable.minimumPayment}`);
    } else {
      console.log(`Using campaign baseline pricing for ${deliverable.name}: $${deliverable.minimumPayment}`);
    }

    return {
      deliverableId: deliverable._id.toString(),
      name: deliverable.name,
      description: deliverable.description,
      dueDate: new Date(
        Date.now() + deliverable.daysToComplete * 24 * 60 * 60 * 1000,
      ),
      compensation,
      requirements: [deliverable.description],
      type: deliverable.type,
    };
  });

  const totalCompensation = contractDeliverables.reduce(
    (sum, d) => sum + d.compensation,
    0,
  );

  // Create default payment schedule if not provided
  const defaultPaymentSchedule = input.paymentSchedule || [
    {
      description: "Full payment upon completion of all deliverables",
      amount: totalCompensation,
      milestone: "Campaign completion",
    },
  ];

  const paymentSchedule = defaultPaymentSchedule.map((payment) => ({
    ...payment,
    dueDate: new Date(campaign.endDate.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days after campaign end
  }));

  // Set contract expiration (default 30 days)
  const expirationDays = input.expirationDays || 30;
  const expiresAt = new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000);

  // Generate contract number
  const timestamp = Date.now().toString(36).toUpperCase();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  const contractNumber = `CONTRACT-${timestamp}-${random}`;

  // Create contract
  const contract = new ContractModel({
    campaignId: campaign._id,
    applicationId: application._id,
    brandId: brand._id,
    athleteId: athlete._id,
    contractNumber,
    type: ContractType.CAMPAIGN_AGREEMENT,
    status: ContractStatus.PENDING_BRAND_REVIEW,
    version: 1,
    title: `Campaign Collaboration Agreement - ${campaign.name}`,
    terms: {
      totalCompensation,
      paymentSchedule,
      deliverables: contractDeliverables,
      campaignDuration: {
        startDate: campaign.startDate,
        endDate: campaign.endDate,
      },
      additionalTerms: input.customTerms?.additionalTerms?.filter(Boolean) || [],
      cancellationPolicy: input.customTerms?.cancellationPolicy,
      intellectualPropertyRights: input.customTerms?.intellectualPropertyRights,
      confidentialityClause: input.customTerms?.confidentialityClause,
    },
    participants: [
      {
        userId: new Types.ObjectId(userId),
        userType: "brand",
      },
      {
        userId: athlete.userId,
        userType: "athlete",
      },
    ],
    expiresAt,
  });

  await contract.save();
  return contract.toClient();
};

export const getContractById = async (
  userId: string,
  userType: UserType,
  contractId: string,
): Promise<SerializedContract> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const hasAccess = await verifyContractAccess(userId, userType, contract);
  if (!hasAccess) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to view this contract",
    );
  }

  // Fetch participant profiles to include in the response
  const [brand, athlete] = await Promise.all([
    BrandModel.findById(contract.brandId).populate('userId', 'name'),
    AthleteModel.findById(contract.athleteId).populate('userId', 'name'),
  ]);

  const serializedContract = contract.toClient();

  // Add participant profile data to the serialized contract
  if (brand || athlete) {
    serializedContract.participantProfiles = {};

    if (brand) {
      serializedContract.participantProfiles.brand = {
        name: (brand.userId as any)?.name || 'Brand User',
        companyName: brand.companyName,
        logo: {
          url: brand.logo.url,
          key: brand.logo.key,
          uploadedAt: brand.logo.uploadedAt?.toISOString() || null,
        },
      };
    }

    if (athlete) {
      serializedContract.participantProfiles.athlete = {
        name: (athlete.userId as any)?.name || athlete.name || 'Athlete User',
        profilePicture: {
          url: athlete.profilePicture.url,
          key: athlete.profilePicture.key,
          uploadedAt: athlete.profilePicture.uploadedAt?.toISOString() || null,
        },
      };
    }
  }

  return serializedContract;
};

export const updateContract = async (
  userId: string,
  userType: UserType,
  input: ContractUpdateInput,
): Promise<SerializedContract> => {
  console.log("Contract update input received:", JSON.stringify(input, null, 2));
  const { contractId, ...updateData } = input;

  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const hasAccess = await verifyContractAccess(userId, userType, contract);
  if (!hasAccess) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to update this contract",
    );
  }

  // Restrict contract editing to before review is complete (DRAFT and PENDING_BRAND_REVIEW only)
  if (!isContractEditingAllowed(contract.status)) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Contract editing is only available before the brand review is complete. Once the review is finished, the contract becomes finalized and cannot be edited.",
    );
  }

  // Only brands can edit contracts
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brands can edit contracts",
    );
  }

  // Update contract fields
  if (updateData.title) {
    contract.title = updateData.title;
  }

  if (updateData.terms) {
    // Update terms object
    if (updateData.terms.additionalTerms !== undefined) {
      contract.terms.additionalTerms = updateData.terms.additionalTerms;
    }
    if (updateData.terms.cancellationPolicy !== undefined) {
      contract.terms.cancellationPolicy = updateData.terms.cancellationPolicy;
    }
    if (updateData.terms.intellectualPropertyRights !== undefined) {
      contract.terms.intellectualPropertyRights = updateData.terms.intellectualPropertyRights;
    }
    if (updateData.terms.confidentialityClause !== undefined) {
      contract.terms.confidentialityClause = updateData.terms.confidentialityClause;
    }
  }

  // Add to revision history
  const changes = [];
  if (updateData.title) changes.push("Title updated");
  if (updateData.terms?.additionalTerms !== undefined) changes.push("Additional terms updated");
  if (updateData.terms?.cancellationPolicy !== undefined) changes.push("Cancellation policy updated");
  if (updateData.terms?.intellectualPropertyRights !== undefined) changes.push("Intellectual property rights updated");
  if (updateData.terms?.confidentialityClause !== undefined) changes.push("Confidentiality clause updated");

  if (changes.length > 0) {
    if (!contract.revisionHistory) {
      contract.revisionHistory = [];
    }
    contract.revisionHistory.push({
      version: contract.version,
      changedBy: new Types.ObjectId(userId) as any,
      changedAt: new Date(),
      changes,
      reason: updateData.reason || "Contract terms updated",
    });
  }

  await contract.save();

  // Return serialized contract
  const serializedContract = contract.toClient();
  return serializedContract;
};

export const updateContractStatus = async (
  userId: string,
  userType: UserType,
  contractId: string,
  status: ContractStatus,
  reason?: string,
): Promise<SerializedContract> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const hasAccess = await verifyContractAccess(userId, userType, contract);
  if (!hasAccess) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to update this contract",
    );
  }

  // Validate status transitions
  const validTransitions = getValidStatusTransitions(contract.status, userType);
  if (!validTransitions.includes(status)) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      `Cannot transition from ${contract.status} to ${status}`,
    );
  }

  // Update contract status and timestamps
  contract.status = status;
  
  switch (status) {
    case ContractStatus.PENDING_BRAND_APPROVAL:
      contract.brandReviewedAt = new Date();
      break;
    case ContractStatus.PENDING_ATHLETE_SIGNATURE:
      contract.brandApprovedAt = new Date();
      contract.sentToAthleteAt = new Date();
      break;
    case ContractStatus.ATHLETE_SIGNED:
      if (userType === "athlete") {
        contract.athleteSignedAt = new Date();
      }
      break;
    case ContractStatus.PENDING_BRAND_SIGNATURE:
      // Contract is ready for brand signature after athlete has signed
      break;
    case ContractStatus.BRAND_SIGNED:
      // Contract signed by brand, now needs payment
      await sendContractNotification(
        contract,
        MessageType.CONTRACT_PAYMENT_REQUIRED,
        `Contract signed and ready for payment: ${contract.title}`,
        userId
      );
      break;
    case ContractStatus.PENDING_PAYMENT:
      // Payment process initiated
      break;
    case ContractStatus.PAID:
      // Payment completed successfully (legacy status)
      await sendContractNotification(
        contract,
        MessageType.CONTRACT_PAYMENT_COMPLETED,
        `Payment completed for contract: ${contract.title}`,
        userId
      );
      break;
    case ContractStatus.AWAITING_DELIVERABLES:
      // Contract payment completed, athlete should start working on deliverables
      await sendContractNotification(
        contract,
        MessageType.CONTRACT_PAYMENT_COMPLETED,
        `🎉 Payment completed for contract: ${contract.title}! You can now start working on the deliverables. Check the contract details for requirements and deadlines.`,
        userId
      );

      // Add earnings to pending when contract becomes active
      try {
        await updatePendingEarnings(contract._id.toString(), true);
        console.log(`[Contract] Added pending earnings for contract ${contract._id}`);
      } catch (error) {
        console.error(`[Contract] Failed to update pending earnings for contract ${contract._id}:`, error);
      }
      break;
    case ContractStatus.FULFILLED:
      // Contract fulfilled
      await sendContractNotification(
        contract,
        MessageType.CONTRACT_FULFILLED,
        `Contract fulfilled: ${contract.title}`,
        userId
      );
      break;
  }

  // Add to revision history if reason provided
  if (reason) {
    if (!contract.revisionHistory) {
      contract.revisionHistory = [];
    }
    contract.revisionHistory.push({
      version: contract.version,
      changedBy: new Types.ObjectId(userId) as any,
      changedAt: new Date(),
      changes: [`Status changed to ${status}`],
      reason,
    });
  }

  await contract.save();

  // Send contract notification based on status change
  const contractData = contract.toClient();
  switch (status) {
    case ContractStatus.PENDING_ATHLETE_SIGNATURE:
      await sendContractNotification(
        contract,
        MessageType.CONTRACT_SENT,
        `A new contract is ready for your review and signature: ${contract.title}`,
        userId
      );
      break;
    case ContractStatus.CANCELLED:
      await sendContractNotification(
        contract,
        MessageType.CONTRACT_CANCELLED,
        `Contract has been cancelled: ${contract.title}${reason ? ` - ${reason}` : ''}`,
        userId
      );
      break;
    default:
      // For other status changes, send a generic update notification
      if (status !== ContractStatus.PENDING_BRAND_REVIEW && status !== ContractStatus.PENDING_BRAND_APPROVAL) {
        await sendContractNotification(
          contract,
          MessageType.CONTRACT_UPDATED,
          `Contract status updated to ${status.toLowerCase().replace(/_/g, ' ')}: ${contract.title}`,
          userId
        );
      }
      break;
  }

  return contractData;
};

export const getBrandContracts = async (
  userId: string,
  userType: UserType,
  status?: ContractStatus,
  search?: string,
  page: number = 1,
  limit: number = 10,
): Promise<{ contracts: SerializedContract[]; total: number; pages: number }> => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can access brand contracts",
    );
  }

  const brand = await BrandModel.findOne({ userId });
  if (!brand) {
    throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
  }

  const query: any = { brandId: brand._id };
  if (status) {
    query.status = status;
  }

  // Add search functionality
  if (search && search.trim()) {
    const searchRegex = new RegExp(search.trim(), 'i');
    query.$or = [
      { title: searchRegex },
      { contractNumber: searchRegex },
      { 'terms.deliverables.description': searchRegex },
    ];
  }

  const skip = (page - 1) * limit;
  const [contracts, total] = await Promise.all([
    ContractModel.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    ContractModel.countDocuments(query),
  ]);

  return {
    contracts: contracts.map((c) => c.toClient()),
    total,
    pages: Math.ceil(total / limit),
  };
};

export const getAthleteContracts = async (
  userId: string,
  userType: UserType,
  status?: ContractStatus,
  search?: string,
  page: number = 1,
  limit: number = 10,
): Promise<{ contracts: SerializedContract[]; total: number; pages: number }> => {
  if (userType !== "athlete") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only athlete users can access athlete contracts",
    );
  }

  const athlete = await AthleteModel.findOne({ userId });
  if (!athlete) {
    throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
  }

  const query: any = { athleteId: athlete._id };
  if (status) {
    query.status = status;
  }

  // Add search functionality
  if (search && search.trim()) {
    const searchRegex = new RegExp(search.trim(), 'i');
    query.$or = [
      { title: searchRegex },
      { contractNumber: searchRegex },
      { 'terms.deliverables.description': searchRegex },
    ];
  }

  const skip = (page - 1) * limit;
  const [contracts, total] = await Promise.all([
    ContractModel.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit),
    ContractModel.countDocuments(query),
  ]);

  return {
    contracts: contracts.map((c) => c.toClient()),
    total,
    pages: Math.ceil(total / limit),
  };
};

// Helper functions
async function verifyContractAccess(
  userId: string,
  userType: UserType,
  contract: any,
): Promise<boolean> {
  if (userType === "brand") {
    const brand = await BrandModel.findOne({ userId });
    return !!(brand && contract.brandId.toString() === brand._id.toString());
  } else if (userType === "athlete") {
    const athlete = await AthleteModel.findOne({ userId });
    return !!(athlete && contract.athleteId.toString() === athlete._id.toString());
  }
  return false;
}

function getValidStatusTransitions(
  currentStatus: ContractStatus,
  userType: UserType,
): ContractStatus[] {
  const transitions: Record<ContractStatus, Record<UserType, ContractStatus[]>> = {
    [ContractStatus.DRAFT]: {
      brand: [ContractStatus.PENDING_BRAND_REVIEW, ContractStatus.CANCELLED],
      athlete: [],
    },
    [ContractStatus.PENDING_BRAND_REVIEW]: {
      brand: [
        ContractStatus.PENDING_BRAND_APPROVAL,
        ContractStatus.DRAFT,
        ContractStatus.CANCELLED,
      ],
      athlete: [],
    },
    [ContractStatus.PENDING_BRAND_APPROVAL]: {
      brand: [
        ContractStatus.PENDING_ATHLETE_SIGNATURE,
        ContractStatus.PENDING_BRAND_REVIEW,
        ContractStatus.CANCELLED,
      ],
      athlete: [],
    },
    [ContractStatus.PENDING_ATHLETE_SIGNATURE]: {
      brand: [ContractStatus.CANCELLED],
      athlete: [ContractStatus.PENDING_BRAND_SIGNATURE, ContractStatus.CANCELLED],
    },
    [ContractStatus.ATHLETE_SIGNED]: {
      brand: [ContractStatus.PENDING_BRAND_SIGNATURE, ContractStatus.CANCELLED],
      athlete: [],
    },
    [ContractStatus.PENDING_BRAND_SIGNATURE]: {
      brand: [ContractStatus.BRAND_SIGNED, ContractStatus.CANCELLED],
      athlete: [],
    },
    [ContractStatus.BRAND_SIGNED]: {
      brand: [ContractStatus.CANCELLED],
      athlete: [],
    },
    [ContractStatus.PENDING_PAYMENT]: {
      brand: [ContractStatus.AWAITING_DELIVERABLES, ContractStatus.CANCELLED],
      athlete: [],
    },
    [ContractStatus.PAID]: {
      brand: [ContractStatus.AWAITING_DELIVERABLES],
      athlete: [],
    },
    [ContractStatus.AWAITING_DELIVERABLES]: {
      brand: [ContractStatus.FULFILLED],
      athlete: [ContractStatus.FULFILLED],
    },
    [ContractStatus.FULFILLED]: {
      brand: [],
      athlete: [],
    },
    [ContractStatus.CANCELLED]: {
      brand: [],
      athlete: [],
    },
    [ContractStatus.EXPIRED]: {
      brand: [],
      athlete: [],
    },
  };

  return transitions[currentStatus]?.[userType] || [];
}



export const generateContractPdf = async (
  userId: string,
  userType: UserType,
  contractId: string,
  regenerate: boolean = false,
): Promise<{ pdfUrl: string; pdfKey: string }> => {
  console.log(`[PDF Generation] Starting PDF generation for contract ${contractId} by user ${userId}`);

  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    console.error(`[PDF Generation] Contract not found: ${contractId}`);
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const hasAccess = await verifyContractAccess(userId, userType, contract);
  if (!hasAccess) {
    console.error(`[PDF Generation] Access denied for user ${userId} to contract ${contractId}`);
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to generate PDF for this contract",
    );
  }

  // Restrict PDF generation to finalized contracts only (after brand review is complete)
  if (!isPdfGenerationAllowed(contract.status)) {
    console.error(`[PDF Generation] PDF generation not allowed for contract status: ${contract.status}`);
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "PDF generation is only available after the brand has finished reviewing the contract. Please complete the review process first.",
    );
  }

  // Check if PDF already exists and regenerate is false
  if (contract.pdfUrl && contract.pdfKey && !regenerate) {
    console.log(`[PDF Generation] Using existing PDF for contract ${contractId}`);
    return {
      pdfUrl: contract.pdfUrl,
      pdfKey: contract.pdfKey,
    };
  }

  console.log(`[PDF Generation] Fetching related data for contract ${contractId}`);

  // Get related data for PDF generation
  const [campaign, brand, athlete] = await Promise.all([
    CampaignModel.findById(contract.campaignId),
    BrandModel.findById(contract.brandId),
    AthleteModel.findById(contract.athleteId).populate('userId'),
  ]);

  if (!campaign || !brand || !athlete) {
    console.error(`[PDF Generation] Missing required data - Campaign: ${!!campaign}, Brand: ${!!brand}, Athlete: ${!!athlete}`);
    throw new ExtendedTRPCError(
      "NOT_FOUND",
      "Required data not found for PDF generation",
    );
  }

  let uploadResult;

  try {
    console.log(`[PDF Generation] Serializing campaign data for contract ${contractId}`);
    // Serialize campaign data
    const serializedCampaign = await serializeCampaign(campaign);

    console.log(`[PDF Generation] Starting PDF generation process for contract ${contractId}`);
    // Generate PDF using pdfmake
    const pdfBuffer = await pdfMakeGenerator.generateContractPdf({
      contract: contract.toClient(),
      campaign: serializedCampaign,
      brand: { ...brand.toObject(), _id: brand._id.toString() },
      athlete: { ...athlete.toObject(), _id: athlete._id.toString() },
    });

    console.log(`[PDF Generation] PDF generated successfully, uploading to S3 for contract ${contractId}`);

    try {
      // Upload PDF directly to S3 (simpler and more reliable than signed URLs)
      const fileName = `contract-${contract.contractNumber}-v${contract.version}.pdf`;
      console.log(`[PDF Generation] Uploading PDF directly to S3 for file: ${fileName}`);

      uploadResult = await uploadPdfDirectly(fileName, pdfBuffer);
      console.log(`[PDF Generation] PDF uploaded successfully to S3 for contract ${contractId}`);

    } catch (s3Error) {
      console.error(`[PDF Generation] S3 upload failed for contract ${contractId}:`, s3Error);
      if (s3Error instanceof ExtendedTRPCError) {
        throw s3Error;
      }
      throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", `S3 upload failed: ${s3Error instanceof Error ? s3Error.message : 'Unknown S3 error'}`);
    }
  } catch (error) {
    console.error(`[PDF Generation] Error during PDF generation for contract ${contractId}:`, error);

    if (error instanceof ExtendedTRPCError) {
      throw error;
    }

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('timeout') || error.message.includes('Timeout')) {
        throw new ExtendedTRPCError("TIMEOUT", "PDF generation timed out. Please try again.");
      }
      if (error.message.includes('memory') || error.message.includes('Memory')) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Insufficient resources for PDF generation. Please try again later.");
      }
      if (error.message.includes('Connection closed') || error.message.includes('Protocol error')) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "PDF generation service temporarily unavailable. Please try again in a moment.");
      }
      if (error.message.includes('Failed to initialize browser') || error.message.includes('Browser')) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "PDF generation service is starting up. Please try again in a few seconds.");
      }
      if (error.message.includes('Maximum retries exceeded') || error.message.includes('retry attempts failed')) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "PDF generation failed after multiple attempts. Please contact support if this persists.");
      }
      if (error.message.includes('S3 upload failed') || error.message.includes('Failed to upload PDF to S3')) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to save PDF file. Please try again.");
      }
      if (error.message.includes('Invalid upload type') || error.message.includes('AWS')) {
        throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "File storage service configuration error. Please contact support.");
      }
    }

    throw new ExtendedTRPCError("INTERNAL_SERVER_ERROR", "Failed to generate PDF. Please try again.");
  }

  // Delete old PDF if it exists
  if (contract.pdfKey && regenerate) {
    try {
      await deleteFile(contract.pdfKey);
    } catch (error) {
      console.warn("Failed to delete old PDF:", error);
    }
  }

  // Update contract with PDF information
  contract.pdfUrl = uploadResult.publicUrl;
  contract.pdfKey = uploadResult.key;
  await contract.save();

  return {
    pdfUrl: uploadResult.publicUrl,
    pdfKey: uploadResult.key,
  };
};

export const signContract = async (
  userId: string,
  userType: UserType,
  contractId: string,
  signatureData: {
    ipAddress: string;
    userAgent: string;
  },
): Promise<SerializedContract> => {
  if (userType !== "athlete" && userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only athletes and brands can sign contracts",
    );
  }

  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract and check status
  if (userType === "athlete") {
    const athlete = await AthleteModel.findOne({ userId });
    if (!athlete || contract.athleteId.toString() !== athlete._id.toString()) {
      throw new ExtendedTRPCError(
        "FORBIDDEN",
        "You don't have permission to sign this contract",
      );
    }

    // Verify contract is in correct status for athlete signing
    if (contract.status !== ContractStatus.PENDING_ATHLETE_SIGNATURE) {
      throw new ExtendedTRPCError(
        "BAD_REQUEST",
        "Contract is not ready for athlete signature",
      );
    }

    // Check if contract has expired
    if (contract.expiresAt && contract.expiresAt < new Date()) {
      contract.status = ContractStatus.EXPIRED;
      await contract.save();
      throw new ExtendedTRPCError("BAD_REQUEST", "Contract has expired");
    }

    // Update athlete participant signature data
    const athleteParticipant = contract.participants.find(
      (p) => p.userType === "athlete",
    );
    if (athleteParticipant) {
      athleteParticipant.signedAt = new Date();
      athleteParticipant.signatureData = {
        ipAddress: signatureData.ipAddress,
        userAgent: signatureData.userAgent,
        timestamp: new Date(),
      };
    }

    // Update contract status and timestamp
    // Automatically transition to PENDING_BRAND_SIGNATURE after athlete signs
    contract.status = ContractStatus.PENDING_BRAND_SIGNATURE;
    contract.athleteSignedAt = new Date();

  } else if (userType === "brand") {
    const brand = await BrandModel.findOne({ userId });
    if (!brand || contract.brandId.toString() !== brand._id.toString()) {
      throw new ExtendedTRPCError(
        "FORBIDDEN",
        "You don't have permission to sign this contract",
      );
    }

    // Verify contract is in correct status for brand signing
    if (contract.status !== ContractStatus.PENDING_BRAND_SIGNATURE) {
      throw new ExtendedTRPCError(
        "BAD_REQUEST",
        "Contract is not ready for brand signature",
      );
    }

    // Update brand participant signature data
    const brandParticipant = contract.participants.find(
      (p) => p.userType === "brand",
    );
    if (brandParticipant) {
      brandParticipant.signedAt = new Date();
      brandParticipant.signatureData = {
        ipAddress: signatureData.ipAddress,
        userAgent: signatureData.userAgent,
        timestamp: new Date(),
      };
    }

    // Update contract status and timestamp
    // Automatically transition to PENDING_PAYMENT after brand signs
    contract.status = ContractStatus.PENDING_PAYMENT;
    contract.brandSignedAt = new Date();
  }

  await contract.save();

  // Send contract signed notification
  await sendContractNotification(
    contract,
    MessageType.CONTRACT_SIGNED,
    `Contract has been signed: ${contract.title}`,
    userId
  );

  // Send payment required notification for brand-signed contracts
  if (userType === "brand") {
    await sendContractNotification(
      contract,
      MessageType.CONTRACT_PAYMENT_REQUIRED,
      `Contract signed and ready for payment: ${contract.title}`,
      userId
    );
  }

  return contract.toClient();
};

export const cancelContract = async (
  userId: string,
  userType: UserType,
  contractId: string,
  reason: string,
): Promise<SerializedContract> => {
  const contract = await ContractModel.findById(contractId);
  if (!contract) {
    throw new ExtendedTRPCError("NOT_FOUND", "Contract not found");
  }

  // Verify user has access to this contract
  const hasAccess = await verifyContractAccess(userId, userType, contract);
  if (!hasAccess) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to cancel this contract",
    );
  }

  // Check if contract can be cancelled
  if (contract.status === ContractStatus.ATHLETE_SIGNED || contract.status === ContractStatus.BRAND_SIGNED) {
    throw new ExtendedTRPCError(
      "BAD_REQUEST",
      "Cannot cancel a signed contract",
    );
  }

  if (contract.status === ContractStatus.CANCELLED) {
    throw new ExtendedTRPCError("BAD_REQUEST", "Contract is already cancelled");
  }

  // Update contract status
  contract.status = ContractStatus.CANCELLED;

  // Add to revision history
  if (!contract.revisionHistory) {
    contract.revisionHistory = [];
  }
  contract.revisionHistory.push({
    version: contract.version,
    changedBy: new Types.ObjectId(userId) as any,
    changedAt: new Date(),
    changes: ["Contract cancelled"],
    reason,
  });

  await contract.save();

  // Send contract cancellation notification
  await sendContractNotification(
    contract,
    MessageType.CONTRACT_CANCELLED,
    `Contract has been cancelled: ${contract.title}${reason ? ` - ${reason}` : ''}`,
    userId
  );

  return contract.toClient();
};

export const getContractsByCampaign = async (
  userId: string,
  userType: UserType,
  campaignId: string,
): Promise<SerializedContract[]> => {
  // Verify user has access to this campaign
  const campaign = await CampaignModel.findById(campaignId);
  if (!campaign) {
    throw new ExtendedTRPCError("NOT_FOUND", "Campaign not found");
  }

  // Check if user has access to this campaign
  let hasAccess = false;
  if (userType === "brand") {
    const brand = await BrandModel.findOne({ userId });
    if (!brand) {
      throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
    }
    hasAccess = campaign.brandId.toString() === brand._id.toString();
  } else if (userType === "athlete") {
    const athlete = await AthleteModel.findOne({ userId });
    if (!athlete) {
      throw new ExtendedTRPCError("NOT_FOUND", "Athlete profile not found");
    }
    // Check if athlete has any contracts for this campaign
    const athleteContract = await ContractModel.findOne({
      campaignId: campaign._id,
      athleteId: athlete._id,
    });
    hasAccess = !!athleteContract;
  }

  if (!hasAccess) {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "You don't have permission to view contracts for this campaign",
    );
  }

  // Fetch contracts for this campaign
  const query: any = { campaignId: campaign._id };

  // If athlete, only show their contracts
  if (userType === "athlete") {
    const athlete = await AthleteModel.findOne({ userId });
    query.athleteId = athlete!._id;
  }

  const contracts = await ContractModel.find(query).sort({ createdAt: -1 });

  return contracts.map((c) => c.toClient());
};

export const getContractByApplication = async (
  userId: string,
  userType: UserType,
  applicationId: string,
): Promise<SerializedContract | null> => {
  // Verify user has access to this application
  const application = await CampaignApplicationModel.findById(applicationId);
  if (!application) {
    throw new ExtendedTRPCError("NOT_FOUND", "Application not found");
  }

  // Check if user has access to this application
  if (userType === "brand") {
    const brand = await BrandModel.findOne({ userId });
    if (!brand) {
      throw new ExtendedTRPCError("NOT_FOUND", "Brand profile not found");
    }

    const campaign = await CampaignModel.findById(application.campaignId);
    if (!campaign || campaign.brandId.toString() !== brand._id.toString()) {
      throw new ExtendedTRPCError(
        "FORBIDDEN",
        "You don't have permission to view this application's contract",
      );
    }
  } else if (userType === "athlete") {
    const athlete = await AthleteModel.findOne({ userId });
    if (!athlete || application.athleteId.toString() !== athlete._id.toString()) {
      throw new ExtendedTRPCError(
        "FORBIDDEN",
        "You don't have permission to view this application's contract",
      );
    }
  }

  // Find contract for this application
  const contract = await ContractModel.findOne({
    applicationId: application._id,
  });

  return contract ? contract.toClient() : null;
};

// Payment-related controller functions
export const createPaymentIntent = async (
  userId: string,
  userType: UserType,
  contractId: string,
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can create payment intents",
    );
  }

  return createContractPaymentIntent({
    contractId,
    brandUserId: userId,
  });
};

export const getPaymentStatus = async (
  userId: string,
  userType: UserType,
  contractId: string,
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can check payment status",
    );
  }

  return getContractPaymentStatus(contractId, userId);
};

export const fulfillContract = async (
  userId: string,
  userType: UserType,
  contractId: string,
) => {
  // Both brands and athletes can mark contracts as fulfilled
  // Brands when they confirm deliverables are complete
  // Athletes when they complete all deliverables

  await markContractFulfilled(contractId, userId);

  // Send notification about contract fulfillment
  const contract = await ContractModel.findById(contractId);
  if (contract) {
    await sendContractNotification(
      contract,
      MessageType.CONTRACT_FULFILLED,
      `Contract fulfilled: ${contract.title}`,
      userId
    );
  }

  return { success: true };
};

export const retryPayment = async (
  userId: string,
  userType: UserType,
  contractId: string,
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can retry payments",
    );
  }

  return retryContractPayment({
    contractId,
    brandUserId: userId,
  });
};

export const syncContractPaymentStatus = async (
  userId: string,
  userType: UserType,
  contractId: string,
) => {
  if (userType !== "brand") {
    throw new ExtendedTRPCError(
      "FORBIDDEN",
      "Only brand users can sync payment status",
    );
  }

  return syncPaymentStatus(contractId, userId);
};
