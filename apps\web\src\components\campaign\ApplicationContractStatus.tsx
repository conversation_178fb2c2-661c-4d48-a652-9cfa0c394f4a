"use client";

import { useState } from "react";
import { trpc } from "@/lib/trpc/client";

import { ApplicationStatus } from "@repo/server/src/types/campaign";
import { SerializedContract, ContractStatus } from "@repo/server/src/types/contract";

import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { ContractGenerationModal } from "../contract/ContractGenerationModal";
import Link from "next/link";

interface ApplicationContractStatusProps {
  application: {
    id: string;
    campaignId: string;
    status: ApplicationStatus;
    athleteId: {
      _id: string;
      userId: {
        _id: string;
        name: string;
      };
    } | string;
  };
  campaignName: string;
  onContractGenerated?: () => void;
}

export function ApplicationContractStatus({
  application,
  campaignName,
  onContractGenerated,
}: ApplicationContractStatusProps) {
  const [showContractModal, setShowContractModal] = useState(false);

  // Fetch contract for this application
  const {
    data: contract,
    isLoading: contractLoading,
    refetch: refetchContract,
  } = trpc.contract.getByApplication.useQuery(
    { applicationId: application.id },
    {
      enabled: application.status === ApplicationStatus.ACCEPTED,
      retry: false,
    }
  );

  const getContractStatusBadge = (contract: SerializedContract | null) => {
    if (!contract) {
      return (
        <Badge variant="outline" className="tw-text-gray-600 tw-border-gray-300">
          No Contract
        </Badge>
      );
    }

    switch (contract.status) {
      case ContractStatus.DRAFT:
        return (
          <Badge className="tw-bg-gray-100 tw-text-gray-800">
            Draft
          </Badge>
        );
      case ContractStatus.PENDING_BRAND_REVIEW:
        return (
          <Badge className="tw-bg-yellow-100 tw-text-yellow-800">
            Pending Review
          </Badge>
        );
      case ContractStatus.PENDING_BRAND_APPROVAL:
        return (
          <Badge className="tw-bg-blue-100 tw-text-blue-800">
            Pending Approval
          </Badge>
        );
      case ContractStatus.PENDING_ATHLETE_SIGNATURE:
        return (
          <Badge className="tw-bg-purple-100 tw-text-purple-800">
            Pending Athlete Signature
          </Badge>
        );
      case ContractStatus.ATHLETE_SIGNED:
        return (
          <Badge className="tw-bg-green-100 tw-text-green-800">
            ✓ Athlete Signed
          </Badge>
        );
      case ContractStatus.PENDING_BRAND_SIGNATURE:
        return (
          <Badge className="tw-bg-blue-100 tw-text-blue-800">
            Pending Brand Signature
          </Badge>
        );
      case ContractStatus.BRAND_SIGNED:
        return (
          <Badge className="tw-bg-green-100 tw-text-green-800">
            ✓ Fully Signed
          </Badge>
        );
      case ContractStatus.PENDING_PAYMENT:
        return (
          <Badge className="tw-bg-yellow-100 tw-text-yellow-800">
            Awaiting Payment
          </Badge>
        );
      case ContractStatus.CANCELLED:
        return (
          <Badge className="tw-bg-red-100 tw-text-red-800">
            Cancelled
          </Badge>
        );
      case "EXPIRED":
        return (
          <Badge className="tw-bg-gray-100 tw-text-gray-600">
            Expired
          </Badge>
        );
      case ContractStatus.PAID:
        return (
          <Badge className="tw-bg-green-100 tw-text-green-800">
            Paid
          </Badge>
        );
        case ContractStatus.FULFILLED:
        return (
          <Badge className="tw-bg-green-100 tw-text-green-800">
            Fulfilled
          </Badge>
        );
        case ContractStatus.PENDING_PAYMENT:
        return (
          <Badge className="tw-bg-orange-100 tw-text-orange-800">
            Pending Payment
          </Badge>
        );
        case ContractStatus.AWAITING_DELIVERABLES:
        return (
          <Badge className="tw-bg-purple-100 tw-text-purple-800">
            Awaiting Deliverables
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            Unknown
          </Badge>
        );
    }
  };

  const getActionButton = () => {
    if (application.status !== ApplicationStatus.ACCEPTED) {
      return null;
    }

    if (contractLoading) {
      return (
        <Button size="sm" disabled>
          Loading...
        </Button>
      );
    }

    if (!contract) {
      return (
        <Button
          size="sm"
          onClick={() => setShowContractModal(true)}
          className="tw-text-black"
        >
          Generate Contract
        </Button>
      );
    }

    // Contract exists - show appropriate action
    switch (contract.status) {
      case ContractStatus.DRAFT:
      case ContractStatus.PENDING_BRAND_REVIEW:
        return (
          <Button
            size="sm"
            variant="outline"
            asChild
          >
            <Link href={`/app/brand/contracts/${contract.id}/#review`}>
              Review Contract
            </Link>
          </Button>
        );
      case ContractStatus.PENDING_BRAND_APPROVAL:
        return (
          <Button
            size="sm"
            asChild
          >
            <Link href={`/app/brand/contracts/${contract.id}/#review`}>
              Approve & Send
            </Link>
          </Button>
        );
      case ContractStatus.PENDING_ATHLETE_SIGNATURE:
        return (
          <Button
            size="sm"
            variant="outline"
            asChild
          >
            <Link href={`/app/athlete/contracts/${contract.id}`}>
            View Contract
            </Link>
          </Button>
        );
      case ContractStatus.ATHLETE_SIGNED:
        return (
          <Button
            size="sm"
            asChild
          >
            <Link href={`/app/athlete/contracts/${contract.id}/#review`}>
              Sign Contract
            </Link>
          </Button>
        );
      case ContractStatus.PENDING_BRAND_SIGNATURE:
        return (
          <Button
            size="sm"
            asChild
          >
            <Link href={`/app/brand/contracts/${contract.id}/#review`}>
            Sign Contract
            </Link>
          </Button>
        );
      case ContractStatus.BRAND_SIGNED:
      case ContractStatus.PENDING_PAYMENT:
        return (
          <Button
            size="sm"
            asChild
            className="tw-text-black"
          >
            <Link href={`/app/brand/contracts/${contract.id}/#review`}>
              Complete Payment
            </Link>
          </Button>
        );
      default:
        return (
          <Button
            size="sm"
            variant="outline"
            asChild
          >
            <Link href={`/app/brand/contracts/${contract.id}`}>
              View Contract
            </Link>
          </Button>
        );
    }
  };

  const handleContractGenerated = (contractId: string) => {
    setShowContractModal(false);
    refetchContract();
    onContractGenerated?.();
    
    // Open the new contract in a new tab
    window.open(`/app/brand/contracts/${contractId}`, '_blank');
  };

  const athleteName = typeof application.athleteId === 'object' 
    ? application.athleteId.userId.name 
    : 'Unknown Athlete';

  return (
    <div className="tw-flex tw-items-center tw-gap-3">
      {/* Contract Status Badge */}
      {getContractStatusBadge(contract ?? null)}
      
      {/* Action Button */}
      {getActionButton()}

      {/* Contract Generation Modal */}
      <ContractGenerationModal
        isOpen={showContractModal}
        onClose={() => setShowContractModal(false)}
        campaignId={application.campaignId}
        applicationId={application.id}
        campaignName={campaignName}
        athleteName={athleteName}
        onSuccess={handleContractGenerated}
      />
    </div>
  );
}
