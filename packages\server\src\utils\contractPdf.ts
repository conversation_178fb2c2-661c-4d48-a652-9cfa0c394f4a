import { ContractStatus } from "../types/contract";

/**
 * Check if PDF generation is allowed for the given contract status
 * PDF generation is only allowed after brand has finished reviewing (PENDING_BRAND_APPROVAL and beyond)
 */
export function isPdfGenerationAllowed(contractStatus: ContractStatus): boolean {
  const allowedStatuses = [
    ContractStatus.PENDING_BRAND_APPROVAL,
    ContractStatus.PENDING_ATHLETE_SIGNATURE,
    ContractStatus.ATHLETE_SIGNED,
    ContractStatus.PENDING_BRAND_SIGNATURE,
    ContractStatus.BRAND_SIGNED,
    ContractStatus.PENDING_PAYMENT,
    ContractStatus.PAID,
    ContractStatus.FULFILLED,
  ];

  return allowedStatuses.includes(contractStatus);
}

/**
 * Get a user-friendly message explaining why PDF generation is not available
 */
export function getPdfGenerationDisabledMessage(contractStatus: ContractStatus): string {
  switch (contractStatus) {
    case ContractStatus.DRAFT:
      return "PDF generation will be available after the contract is submitted for review.";
    case ContractStatus.PENDING_BRAND_REVIEW:
      return "PDF generation will be available after the brand completes their review.";
    default:
      return "PDF generation is not available for this contract status.";
  }
}

/**
 * Check if contract editing is allowed for the given contract status
 * Contract editing is only allowed before brand review is complete (DRAFT and PENDING_BRAND_REVIEW)
 */
export function isContractEditingAllowed(contractStatus: ContractStatus): boolean {
  const allowedStatuses = [
    ContractStatus.DRAFT,
    ContractStatus.PENDING_BRAND_REVIEW,
  ];

  return allowedStatuses.includes(contractStatus);
}

/**
 * Get a user-friendly message explaining why contract editing is not available
 */
export function getContractEditingDisabledMessage(contractStatus: ContractStatus): string {
  switch (contractStatus) {
    case ContractStatus.PENDING_BRAND_APPROVAL:
      return "Contract editing is no longer available after the review is complete.";
    case ContractStatus.PENDING_ATHLETE_SIGNATURE:
    case ContractStatus.ATHLETE_SIGNED:
    case ContractStatus.PENDING_BRAND_SIGNATURE:
    case ContractStatus.BRAND_SIGNED:
    case ContractStatus.PENDING_PAYMENT:
    case ContractStatus.PAID:
    case ContractStatus.FULFILLED:
      return "Contract cannot be edited after it has been finalized.";
    case ContractStatus.CANCELLED:
      return "Cancelled contracts cannot be edited.";
    case ContractStatus.EXPIRED:
      return "Expired contracts cannot be edited.";
    default:
      return "Contract editing is not available for this status.";
  }
}
