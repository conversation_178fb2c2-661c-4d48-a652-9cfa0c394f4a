import { Schema } from "mongoose";

import { Deliverable } from "../models/deliverable";

export enum ApplicationStatus {
  PENDING = "PENDING",
  ACCEPTED = "ACCEPTED",
  REJECTED = "REJECTED",
  WITHDRAWN = "WITHDRAWN",
}

export enum CampaignStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  PAUSED = "paused",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

export enum CampaignVisibility {
  PUBLIC = "public",
  PRIVATE = "private",
}

export interface Campaign {
  brandId: Schema.Types.ObjectId;
  name: string;
  description: string;
  deliverables: Deliverable[];
  startDate: Date;
  endDate: Date;
  status: CampaignStatus;
  visibility: CampaignVisibility;
  athletes?: Schema.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
  price: number;
  interests: string[];
  files?: { url: string; originalName: string }[];
}

export interface CampaignApplication {
  campaignId: Schema.Types.ObjectId;
  athleteId: Schema.Types.ObjectId;
  status: ApplicationStatus;
  appliedAt: Date;
  updatedAt: Date;
  message?: string;
  compensation?: number;
  deliverables?: Deliverable[];
  initiatedBy: "brand" | "athlete";
}

export interface SerializedCampaign
  extends Omit<
    Campaign,
    | "brandId"
    | "createdAt"
    | "updatedAt"
    | "startDate"
    | "endDate"
    | "athletes"
    | "deliverables"
  > {
  brandId: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  startDate: string;
  endDate: string;
  athletes?: {
    _id: string;
    profilePicture: {
      url: string;
    };
  }[];
  deliverables: Deliverable[];
  visibility: CampaignVisibility;
  price: number;
  interests: string[];
  files?: { url: string; originalName: string }[];
}

export interface SerializedCampaignApplication
  extends Omit<CampaignApplication, "campaignId" | "athleteId"> {
  campaignId: string;
  athleteId:
    | {
        _id: string;
        userId: {
          _id: string;
          name: string;
        };
        profilePicture?: {
          url: string;
        };
      }
    | string;
  id: string;
}

export interface SerializedCampaign {
  id: string;
  brandId: string;
  name: string;
  description: string;
  deliverables: Deliverable[];
  startDate: string;
  endDate: string;
  status: CampaignStatus;
  visibility: CampaignVisibility;
  createdAt: string;
  updatedAt: string;
  price: number;
  interests: string[];
  files?: { url: string; originalName: string }[];
}
